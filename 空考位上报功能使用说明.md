# 空考位上报功能使用说明

## 功能概述

本功能在PlatformDataService中新增了空考位数据上报方法，用于将空考位数据通过ProvincePlatformClient的sbkkw接口上报到上级平台。

## 实现内容

### 1. 接口定义

在`PlatformDataService`接口中新增方法：

```java
/**
 * 空考位数据上报到上级平台
 * @param ksjhbh 考试计划编号
 * @param ccm 场次码
 */
void kkwUpload(String ksjhbh, String ccm);
```

### 2. 实现逻辑

在`PlatformDataServiceImpl`中实现了完整的空考位上报逻辑：

#### 2.1 数据查询
- 从`ks_kkw_msg`表中查询未上报的空考位数据
- 查询条件：`report_flag is null or report_flag = '0' or report_flag = '-1'`

#### 2.2 数据转换
- 将`KsKkwMsg`实体转换为`SbkkwDTO`格式
- 按设备序列号分组进行批量上报
- 按考场分组构建考位列表
- 构建已上报核验信息和入场信息

#### 2.3 上报处理
- 使用`ProvincePlatformClient.buildSbkkwRequest()`构建请求
- 调用`ProvincePlatformClient.sbkkw()`接口上报数据
- 根据上报结果更新`report_flag`字段：
  - `"1"`: 上报成功
  - `"-1"`: 上报失败
  - 同时更新`report_time`字段

#### 2.4 平台支持
- 目前仅支持HISOME平台的空考位上报
- 其他平台会记录日志并跳过处理

### 3. 数据格式转换

#### 3.1 基本信息映射
```java
// KsKkwMsg -> SbkkwDTO.SbkkwDataItem
dataItem.setExamPlanCode(msg.getKsjhbh());        // 考试编号
dataItem.setOrgCode(msg.getBzhkdid());            // 机构编号
dataItem.setSn(msg.getSn());                      // 设备序列号
dataItem.setDevType(msg.getDevType());            // 设备类型
dataItem.setCcm(msg.getCcm());                    // 场次码
dataItem.setBzhkcid(msg.getBzhkcid());            // 标准化考场id
```

#### 3.2 考生位号详情映射
```java
// KsKkwMsg -> SbkkwDTO.KzwhxqItem
kzwhxqItem.setZkzh(msg.getKsZkzh());              // 准考证号
kzwhxqItem.setBpzwh(msg.getKsBpzwh());            // 编排座位号
kzwhxqItem.setSjzwh(msg.getKsSjzwh());            // 实际座位号
kzwhxqItem.setRcbz(msg.getRcbz());                // 入场备注
kzwhxqItem.setSjyxj(msg.getSjyxj().toString());   // 数据优先级
```

#### 3.3 核验信息构建
根据`KsKkwMsg`中的验证信息构建`YsbhyxxItem`：
- 验证时间：优先使用`rcsj`，其次使用`czsj`
- 核验结果：根据`sfrc`字段判断
- 各项验证结果：解析`yzfs`和`yzjg`字段

#### 3.4 入场信息构建
根据`KsKkwMsg`中的入场信息构建`YsbrcxxItem`：
- 上报时间：优先使用`report_time`，其次使用`czsj`
- 入场状态：使用`rgyzjg`字段

## 使用方式

### 1. 直接调用
```java
@Resource
private PlatformDataService platformDataService;

// 上报指定考试计划和场次的空考位数据
platformDataService.kkwUpload("KSJHBH001", "001");
```

### 2. 定时任务调用
可以在定时任务中调用此方法，定期上报空考位数据：

```java
@Scheduled(fixedRate = 300000) // 每5分钟执行一次
public void scheduleKkwUpload() {
    // 获取需要上报的考试计划和场次列表
    List<KsKscc> ksccList = getActiveKsccList();
    
    for (KsKscc kscc : ksccList) {
        try {
            platformDataService.kkwUpload(kscc.getKsjhbh(), kscc.getCcm());
        } catch (Exception e) {
            log.error("空考位数据上报失败", e);
        }
    }
}
```

### 3. 手动触发
可以在管理界面提供手动触发按钮，调用此方法进行即时上报。

## 注意事项

1. **平台限制**：目前仅支持HISOME平台，其他平台会跳过处理
2. **数据状态**：只会上报未上报或上报失败的数据
3. **批量处理**：按设备序列号分组进行批量上报，提高效率
4. **错误处理**：上报失败时会更新状态并记录错误日志
5. **事务处理**：建议在调用时考虑事务边界，避免数据不一致

## 相关表结构

### ks_kkw_msg表字段说明
- `report_flag`: 上报状态（0-未上报，1-已上报，-1-上报失败）
- `report_time`: 上报时间
- 其他字段参考`KsKkwMsg`实体类定义

## 测试验证

提供了完整的单元测试用例`PlatformDataServiceKkwUploadTest`，包括：
- 正常上报成功场景
- 无数据场景
- 不支持平台场景

可以运行测试用例验证功能的正确性。
