-- 空考位上报功能测试数据
-- 创建时间：2025-07-30
-- 说明：包含空考位上报功能所需的完整测试数据

-- ============================================
-- 1. 清理旧数据（可选，仅测试环境使用）
-- ============================================
-- DELETE FROM ks_kkw_msg WHERE ksjhbh IN ('TEST_KSJH_001', 'TEST_KSJH_002');
-- DELETE FROM ks_ksjh WHERE ksjhbh IN ('TEST_KSJH_001', 'TEST_KSJH_002');
-- DELETE FROM ks_kdxx WHERE ksjhbh IN ('TEST_KSJH_001', 'TEST_KSJH_002');
-- DELETE FROM ks_kcxx WHERE ksjhbh IN ('TEST_KSJH_001', 'TEST_KSJH_002');

-- ============================================
-- 2. 系统基础配置数据
-- ============================================

-- 设置默认上级平台为HISOME（支持空考位上报）
INSERT INTO jy_sys_dict (id, t_code, t_value, t_desc, create_time, update_time)
VALUES ('DICT_001', 'DEFAULT_PLAT', 'HISOME', '默认上级平台配置', NOW(), NOW())
ON DUPLICATE KEY UPDATE t_value = 'HISOME', update_time = NOW();

-- 学校基本信息
INSERT INTO cs_xxjbxx (xxdm, xxmc, zzjgm, xxdz, xzqhm)
VALUES ('BZHKD001', '测试学校', 'BZHKDJG1', '测试地址123号', '339005');

-- ============================================
-- 3. 考试计划数据
-- ============================================

-- 考试计划1：包含未上报数据
INSERT INTO ks_ksjh (ksjhbh, mc, kssj, jssj, cjlx, kszt, cjsj, xgsj, scztw)
VALUES ('TEST_KSJH_002', '2025年春季期末考试', '2025-01-15 08:00:00', '2025-01-17 18:00:00', '1', '1', NOW(), NOW(), '0');

-- ============================================
-- 4. 考点信息数据
-- ============================================

-- 测试考点1
INSERT INTO ks_kdxx (bzhkdid, bzhkdmc, ksjhbh, create_time, update_time, scztw)
VALUES ('KD_TEST_001', '测试考点一', 'TEST_KSJH_001', NOW(), NOW(), '0');

-- 测试考点2
INSERT INTO ks_kdxx (bzhkdid, bzhkdmc, ksjhbh, xxdz, lxdh, create_time, update_time, scztw)
VALUES ('KD_TEST_002', '测试考点二', 'TEST_KSJH_002', '考点地址2号', '0571-22222222', NOW(), NOW(), '0');

-- ============================================
-- 5. 考场信息数据
-- ============================================

-- 考试计划1的考场
INSERT INTO ks_kcxx (id, ksjhbh, ccm, kcbh, bzhkcmc, bzhkcid, ljkcbh, zwrs, create_time, update_time, sczt) VALUES
('KC_TEST_001', 'TEST_KSJH_001', '001', 'KC001', '第一考场', 'BZHKC_001', 'LJKC_001', 30, NOW(), NOW(), '0'),
('KC_TEST_002', 'TEST_KSJH_001', '001', 'KC002', '第二考场', 'BZHKC_002', 'LJKC_002', 30, NOW(), NOW(), '0'),
('KC_TEST_003', 'TEST_KSJH_001', '002', 'KC003', '第三考场', 'BZHKC_003', 'LJKC_003', 30, NOW(), NOW(), '0'),
('KC_TEST_004', 'TEST_KSJH_002', '001', 'KC004', '第四考场', 'BZHKC_004', 'LJKC_004', 30, NOW(), NOW(), '0');

-- ============================================
-- 6. 空考位消息测试数据（核心数据）
-- ============================================

-- 场景1：未上报的空考位数据（用于测试分批上报功能）
-- 考试计划1，场次1，设备SN001（50条数据，验证分批逻辑）
INSERT INTO ks_kkw_msg (
    id, ksjhbh, ccm, bzhkdid, bzhkcid, sn, sczt,
    create_time, update_time, ks_zkzh, ks_bpzwh, ks_sjzwh, ks_kkw,
    zcqswzm, zwbjfsm, zwplfsm, ljkch, kch, dev_type, rcbz, sjyxj,
    timestamp, yzfs, yzjg, sfrc, rcsj, rcsjfz, rgyzjg, sjly, czsj,
    report_flag, report_time
) VALUES
-- 设备SN001的数据（25条）
('KKW_001_001', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010001', '01', '01', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:30:00', '2025-01-15 08:30:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_002', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010002', '02', '02', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:32:00', '2025-01-15 08:32:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_003', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010003', '03', '03', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:34:00', '2025-01-15 08:34:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_004', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010004', '04', '04', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:36:00', '2025-01-15 08:36:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_005', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010005', '05', '05', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:38:00', '2025-01-15 08:38:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_006', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010006', '06', '06', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:40:00', '2025-01-15 08:40:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_007', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010007', '07', '07', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:42:00', '2025-01-15 08:42:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_008', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010008', '08', '08', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:44:00', '2025-01-15 08:44:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_009', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010009', '09', '09', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:46:00', '2025-01-15 08:46:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_010', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010010', '10', '10', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:48:00', '2025-01-15 08:48:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_011', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010011', '11', '11', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:50:00', '2025-01-15 08:50:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_012', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010012', '12', '12', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:52:00', '2025-01-15 08:52:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_013', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010013', '13', '13', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:54:00', '2025-01-15 08:54:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_014', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010014', '14', '14', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:56:00', '2025-01-15 08:56:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_015', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010015', '15', '15', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:58:00', '2025-01-15 08:58:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_016', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010016', '16', '16', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:00:00', '2025-01-15 09:00:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_017', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010017', '17', '17', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:02:00', '2025-01-15 09:02:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_018', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010018', '18', '18', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:04:00', '2025-01-15 09:04:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_019', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010019', '19', '19', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:06:00', '2025-01-15 09:06:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_020', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010020', '20', '20', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:08:00', '2025-01-15 09:08:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_021', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010021', '21', '21', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:10:00', '2025-01-15 09:10:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_022', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010022', '22', '22', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:12:00', '2025-01-15 09:12:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_023', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010023', '23', '23', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:14:00', '2025-01-15 09:14:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_024', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010024', '24', '24', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:16:00', '2025-01-15 09:16:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_001_025', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_001', 'SN001', '0', NOW(), NOW(), '2025010025', '25', '25', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_001', 'KC001', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:18:00', '2025-01-15 09:18:05', '1', 'DEVICE', NOW(), NULL, NULL);

-- 设备SN002的数据（另外25条，用于测试多设备场景）
INSERT INTO ks_kkw_msg (
    id, ksjhbh, ccm, bzhkdid, bzhkcid, sn, sczt,
    create_time, update_time, ks_zkzh, ks_bpzwh, ks_sjzwh, ks_kkw,
    zcqswzm, zwbjfsm, zwplfsm, ljkch, kch, dev_type, rcbz, sjyxj,
    timestamp, yzfs, yzjg, sfrc, rcsj, rcsjfz, rgyzjg, sjly, czsj,
    report_flag, report_time
) VALUES
('KKW_002_001', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010026', '01', '01', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:30:00', '2025-01-15 08:30:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_002', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010027', '02', '02', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:32:00', '2025-01-15 08:32:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_003', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010028', '03', '03', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:34:00', '2025-01-15 08:34:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_004', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010029', '04', '04', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:36:00', '2025-01-15 08:36:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_005', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010030', '05', '05', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:38:00', '2025-01-15 08:38:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_006', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010031', '06', '06', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:40:00', '2025-01-15 08:40:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_007', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010032', '07', '07', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:42:00', '2025-01-15 08:42:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_008', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010033', '08', '08', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:44:00', '2025-01-15 08:44:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_009', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010034', '09', '09', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:46:00', '2025-01-15 08:46:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_010', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010035', '10', '10', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:48:00', '2025-01-15 08:48:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_011', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010036', '11', '11', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:50:00', '2025-01-15 08:50:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_012', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010037', '12', '12', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:52:00', '2025-01-15 08:52:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_013', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010038', '13', '13', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:54:00', '2025-01-15 08:54:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_014', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010039', '14', '14', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:56:00', '2025-01-15 08:56:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_015', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010040', '15', '15', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 08:58:00', '2025-01-15 08:58:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_016', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010041', '16', '16', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:00:00', '2025-01-15 09:00:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_017', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010042', '17', '17', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:02:00', '2025-01-15 09:02:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_018', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010043', '18', '18', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:04:00', '2025-01-15 09:04:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_019', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010044', '19', '19', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:06:00', '2025-01-15 09:06:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_020', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010045', '20', '20', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:08:00', '2025-01-15 09:08:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_021', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010046', '21', '21', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:10:00', '2025-01-15 09:10:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_022', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010047', '22', '22', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:12:00', '2025-01-15 09:12:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_023', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010048', '23', '23', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:14:00', '2025-01-15 09:14:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_024', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010049', '24', '24', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:16:00', '2025-01-15 09:16:05', '1', 'DEVICE', NOW(), NULL, NULL),
('KKW_002_025', 'TEST_KSJH_001', '001', 'KD_TEST_001', 'BZHKC_002', 'SN002', '0', NOW(), NOW(), '2025010050', '25', '25', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_002', 'KC002', '1', '1', 20, NOW(), '123', '111', '1', '2025-01-15 09:18:00', '2025-01-15 09:18:05', '1', 'DEVICE', NOW(), NULL, NULL);

-- 场景2：已上报成功的数据（用于测试统计功能）
INSERT INTO ks_kkw_msg (
    id, ksjhbh, ccm, bzhkdid, bzhkcid, sn, sczt,
    create_time, update_time, ks_zkzh, ks_bpzwh, ks_sjzwh, ks_kkw,
    zcqswzm, zwbjfsm, zwplfsm, ljkch, kch, dev_type, rcbz, sjyxj,
    timestamp, yzfs, yzjg, sfrc, rcsj, rcsjfz, rgyzjg, sjly, czsj,
    report_flag, report_time
) VALUES
('KKW_SUCCESS_001', 'TEST_KSJH_002', '001', 'KD_TEST_002', 'BZHKC_004', 'SN003', '0', NOW(), NOW(), '2025010051', '01', '01', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_004', 'KC004', '1', '1', 20, DATE_SUB(NOW(), INTERVAL 1 HOUR), '123', '111', '1', '2025-01-10 08:30:00', '2025-01-10 08:30:05', '1', 'DEVICE', DATE_SUB(NOW(), INTERVAL 1 HOUR), '1', DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
('KKW_SUCCESS_002', 'TEST_KSJH_002', '001', 'KD_TEST_002', 'BZHKC_004', 'SN003', '0', NOW(), NOW(), '2025010052', '02', '02', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_004', 'KC004', '1', '1', 20, DATE_SUB(NOW(), INTERVAL 1 HOUR), '123', '111', '1', '2025-01-10 08:32:00', '2025-01-10 08:32:05', '1', 'DEVICE', DATE_SUB(NOW(), INTERVAL 1 HOUR), '1', DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
('KKW_SUCCESS_003', 'TEST_KSJH_002', '001', 'KD_TEST_002', 'BZHKC_004', 'SN003', '0', NOW(), NOW(), '2025010053', '03', '03', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_004', 'KC004', '1', '1', 20, DATE_SUB(NOW(), INTERVAL 1 HOUR), '123', '111', '1', '2025-01-10 08:34:00', '2025-01-10 08:34:05', '1', 'DEVICE', DATE_SUB(NOW(), INTERVAL 1 HOUR), '1', DATE_SUB(NOW(), INTERVAL 30 MINUTE));

-- 场景3：上报失败的数据（用于测试重试功能）
INSERT INTO ks_kkw_msg (
    id, ksjhbh, ccm, bzhkdid, bzhkcid, sn, sczt,
    create_time, update_time, ks_zkzh, ks_bpzwh, ks_sjzwh, ks_kkw,
    zcqswzm, zwbjfsm, zwplfsm, ljkch, kch, dev_type, rcbz, sjyxj,
    timestamp, yzfs, yzjg, sfrc, rcsj, rcsjfz, rgyzjg, sjly, czsj,
    report_flag, report_time
) VALUES
('KKW_FAILED_001', 'TEST_KSJH_001', '002', 'KD_TEST_001', 'BZHKC_003', 'SN004', '0', NOW(), NOW(), '2025010054', '01', '01', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_003', 'KC003', '1', '1', 20, DATE_SUB(NOW(), INTERVAL 2 HOUR), '123', '111', '1', '2025-01-15 10:30:00', '2025-01-15 10:30:05', '1', 'DEVICE', DATE_SUB(NOW(), INTERVAL 2 HOUR), '-1', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('KKW_FAILED_002', 'TEST_KSJH_001', '002', 'KD_TEST_001', 'BZHKC_003', 'SN004', '0', NOW(), NOW(), '2025010055', '02', '02', '1', '指纹识别', '身份证验证', '人脸识别', 'LJKC_003', 'KC003', '1', '1', 20, DATE_SUB(NOW(), INTERVAL 2 HOUR), '123', '111', '1', '2025-01-15 10:32:00', '2025-01-15 10:32:05', '1', 'DEVICE', DATE_SUB(NOW(), INTERVAL 2 HOUR), '-1', DATE_SUB(NOW(), INTERVAL 1 HOUR));

-- 场景4：大批量数据（用于测试分批上报，生成120条数据确保超过100条的批次限制）
INSERT INTO ks_kkw_msg (
    id, ksjhbh, ccm, bzhkdid, bzhkcid, sn, sczt,
    create_time, update_time, ks_zkzh, ks_bpzwh, ks_sjzwh, ks_kkw,
    zcqswzm, zwbjfsm, zwplfsm, ljkch, kch, dev_type, rcbz, sjyxj,
    timestamp, yzfs, yzjg, sfrc, rcsj, rcsjfz, rgyzjg, sjly, czsj,
    report_flag, report_time
)
SELECT
    CONCAT('KKW_BATCH_', LPAD(@row_number := @row_number + 1, 3, '0')) as id,
    'TEST_KSJH_001' as ksjhbh,
    '003' as ccm,
    'KD_TEST_001' as bzhkdid,
    'BZHKC_001' as bzhkcid,
    'SN005' as sn,
    '0' as sczt,
    NOW() as create_time,
    NOW() as update_time,
    CONCAT('202501', LPAD(@row_number, 4, '0')) as ks_zkzh,
    LPAD(@row_number, 2, '0') as ks_bpzwh,
    LPAD(@row_number, 2, '0') as ks_sjzwh,
    '1' as ks_kkw,
    '指纹识别' as zcqswzm,
    '身份证验证' as zwbjfsm,
    '人脸识别' as zwplfsm,
    'LJKC_001' as ljkch,
    'KC001' as kch,
    '1' as dev_type,
    '1' as rcbz,
    20 as sjyxj,
    NOW() as timestamp,
    '123' as yzfs,
    '111' as yzjg,
    '1' as sfrc,
    DATE_FORMAT(DATE_ADD(NOW(), INTERVAL @row_number MINUTE), '%Y-%m-%d %H:%i:%s') as rcsj,
    DATE_FORMAT(DATE_ADD(NOW(), INTERVAL @row_number MINUTE + 5), '%Y-%m-%d %H:%i:%s') as rcsjfz,
    '1' as rgyzjg,
    'DEVICE' as sjly,
    NOW() as czsj,
    NULL as report_flag,
    NULL as report_time
FROM (
    SELECT @row_number := 100
) r
CROSS JOIN (
    SELECT 1 as n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION
    SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION
    SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION
    SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION
    SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24
) numbers
WHERE @row_number <= 123;

-- ============================================
-- 7. 考生入场信息数据（与空考位相关）
-- ============================================

-- 测试考生入场信息（用于关联查询和统计）
INSERT INTO ks_ksrcxx (
    id, ksjhbh, ccm, zkzh, xm, sfzh, xb, mz, csrq, zp, sfzp,
    kch, zwh, kssj, jssj, sfrc, rcsj, rcrlzp, ljkcbh, bzhkcid, bzhkdid,
    yzfs, yzjg, rgyzjg, sbzt, tbzt, create_time, update_time, scztw
) VALUES
('KSRC_001', 'TEST_KSJH_001', '001', '2025010001', '张三', '330101199001010001', '1', '01', '1990-01-01', NULL, NULL, 'KC001', '01', '2025-01-15 08:00:00', '2025-01-15 10:00:00', '1', '2025-01-15 08:30:00', NULL, 'LJKC_001', 'BZHKC_001', 'KD_TEST_001', '123', '111', '1', '1', '1', NOW(), NOW(), '0'),
('KSRC_002', 'TEST_KSJH_001', '001', '2025010002', '李四', '330101199002020002', '0', '01', '1990-02-02', NULL, NULL, 'KC001', '02', '2025-01-15 08:00:00', '2025-01-15 10:00:00', '1', '2025-01-15 08:32:00', NULL, 'LJKC_001', 'BZHKC_001', 'KD_TEST_001', '123', '111', '1', '1', '1', NOW(), NOW(), '0'),
('KSRC_003', 'TEST_KSJH_002', '001', '2025010051', '王五', '330101199003030003', '1', '01', '1990-03-03', NULL, NULL, 'KC004', '01', '2025-01-10 08:00:00', '2025-01-10 10:00:00', '1', '2025-01-10 08:30:00', NULL, 'LJKC_004', 'BZHKC_004', 'KD_TEST_002', '123', '111', '1', '1', '1', NOW(), NOW(), '0');

-- ============================================
-- 8. 设备信息数据
-- ============================================

-- 测试设备信息
INSERT INTO sb_sbxx (id, xlh, sbmc, sblx, ip, mac, zt, create_time, update_time, sczt) VALUES
('SB_001', 'SN001', '身份验证终端01', '身份验证终端', '*************', '00:11:22:33:44:01', '1', NOW(), NOW(), '0'),
('SB_002', 'SN002', '身份验证终端02', '身份验证终端', '*************', '00:11:22:33:44:02', '1', NOW(), NOW(), '0'),
('SB_003', 'SN003', '身份验证终端03', '身份验证终端', '*************', '00:11:22:33:44:03', '1', NOW(), NOW(), '0'),
('SB_004', 'SN004', '身份验证终端04', '身份验证终端', '*************', '00:11:22:33:44:04', '1', NOW(), NOW(), '0'),
('SB_005', 'SN005', '身份验证终端05', '身份验证终端', '*************', '00:11:22:33:44:05', '1', NOW(), NOW(), '0');

-- ============================================
-- 9. 计算机基本信息数据
-- ============================================

-- 计算机信息（与考场关联）
INSERT INTO cs_jsjbxx (id, bzhkcid, jsmc, ip, mac, zt, create_time, update_time, sczt) VALUES
('JSJ_001', 'BZHKC_001', '第一考场服务器', '*************', '00:11:22:33:55:01', '1', NOW(), NOW(), '0'),
('JSJ_002', 'BZHKC_002', '第二考场服务器', '*************', '00:11:22:33:55:02', '1', NOW(), NOW(), '0'),
('JSJ_003', 'BZHKC_003', '第三考场服务器', '*************', '00:11:22:33:55:03', '1', NOW(), NOW(), '0'),
('JSJ_004', 'BZHKC_004', '第四考场服务器', '*************', '00:11:22:33:55:04', '1', NOW(), NOW(), '0');

-- ============================================
-- 10. 验证查询SQL（可用于测试验证）
-- ============================================

/*
-- 查询未上报的空考位数据
SELECT COUNT(*) as 未上报数量 FROM ks_kkw_msg
WHERE ksjhbh = 'TEST_KSJH_001' AND ccm = '001'
AND sczt = '0' AND (report_flag IS NULL OR report_flag = '0' OR report_flag = '-1');

-- 查询已上报成功的数据
SELECT COUNT(*) as 已上报数量 FROM ks_kkw_msg
WHERE ksjhbh = 'TEST_KSJH_002' AND ccm = '001'
AND sczt = '0' AND report_flag = '1';

-- 查询大批量测试数据
SELECT COUNT(*) as 大批量数据 FROM ks_kkw_msg
WHERE ksjhbh = 'TEST_KSJH_001' AND ccm = '003'
AND sczt = '0' AND report_flag IS NULL;

-- 按设备分组统计
SELECT sn, COUNT(*) as 数量, report_flag as 上报状态
FROM ks_kkw_msg
WHERE ksjhbh IN ('TEST_KSJH_001', 'TEST_KSJH_002')
GROUP BY sn, report_flag
ORDER BY sn, report_flag;

-- 测试空考位统计功能
SELECT
    kc.kcbh as 考场编号,
    COALESCE(kc.bzhkcmc, js.jsmc) as 考场名称,
    COUNT(msg.id) as 空考位数量,
    MAX(msg.czsj) as 最后操作时间
FROM ks_kcxx kc
LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.ljkch
    AND msg.sczt = '0'
    AND kc.ksjhbh = msg.ksjhbh
    AND kc.ccm = msg.ccm
LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0'
WHERE kc.sczt = '0' AND kc.ksjhbh = 'TEST_KSJH_001'
GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc
ORDER BY kc.kcbh;
*/

-- ============================================
-- 数据创建完成，测试场景说明：
-- ============================================
/*
测试场景说明：

1. 分批上报测试：
   - TEST_KSJH_001 + 场次001：50条未上报数据（验证分批逻辑）
   - TEST_KSJH_001 + 场次003：123条未上报数据（验证超过100条的分批）

2. 多设备测试：
   - 数据分布在不同设备序列号（SN001, SN002, SN003, SN004, SN005）

3. 上报状态测试：
   - 未上报：report_flag 为 NULL
   - 已上报：report_flag = '1'
   - 上报失败：report_flag = '-1'

4. 统计功能测试：
   - 已配置考场、考点、设备等关联数据
   - 支持空考位统计查询功能

5. 平台配置测试：
   - 已设置默认平台为HISOME（支持空考位上报）

使用方法：
1. 执行此SQL文件创建测试数据
2. 调用空考位上报接口进行测试
3. 观察日志中的分批处理过程
4. 验证上报状态的更新
*/
