package com.xcwlkj.identityverify.third.superior.http.service;

import com.xcwlkj.identityverify.model.dto.sjscgl.*;

public interface PlatformDataService {
    /**
     * 考生数据上报到上级平台
     */
    void ksxxUpload(KssjscDTO dto);

    /**
     * 考生照片上报到上级平台
     */
    void kszpUpload(KszpscDTO dto);

    /**
     * 监考数据上传到上级平台
     */
    void jksjUpload(JksjscDTO dto);

    /**
     * 监考照片上传到上级平台
     */
    void jkzpUpload(JkzpscDTO dto);

    /**
     * 空考位数据上报到上级平台
     */
    void kkwUpload(SckkwDTO dto);
}
