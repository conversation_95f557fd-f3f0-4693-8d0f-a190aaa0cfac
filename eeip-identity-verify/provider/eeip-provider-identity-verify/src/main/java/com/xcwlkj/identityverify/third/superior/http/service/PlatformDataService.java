package com.xcwlkj.identityverify.third.superior.http.service;

import com.xcwlkj.identityverify.model.dto.sjscgl.JksjscDTO;
import com.xcwlkj.identityverify.model.dto.sjscgl.JkzpscDTO;
import com.xcwlkj.identityverify.model.dto.sjscgl.KssjscDTO;
import com.xcwlkj.identityverify.model.dto.sjscgl.KszpscDTO;

public interface PlatformDataService {
    /**
     * 考生数据上报到上级平台
     */
    void ksxxUpload(KssjscDTO dto);

    /**
     * 考生照片上报到上级平台
     */
    void kszpUpload(KszpscDTO dto);

    /**
     * 监考数据上传到上级平台
     */
    void jksjUpload(JksjscDTO dto);

    /**
     * 监考照片上传到上级平台
     */
    void jkzpUpload(JkzpscDTO dto);

    /**
     * 空考位数据上报到上级平台
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     */
    void kkwUpload(String ksjhbh, String ccm);
}
