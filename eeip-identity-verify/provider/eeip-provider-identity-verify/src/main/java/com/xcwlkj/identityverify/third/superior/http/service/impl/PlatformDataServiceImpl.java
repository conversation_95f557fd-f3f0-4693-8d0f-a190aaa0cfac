package com.xcwlkj.identityverify.third.superior.http.service.impl;

import com.xcwlkj.exceptions.IdentityVerifyException;
import com.xcwlkj.identityverify.model.constant.GlobalKeys;
import com.xcwlkj.identityverify.model.domain.JySysDict;
import com.xcwlkj.identityverify.model.domain.KsKkwMsg;
import com.xcwlkj.identityverify.model.domain.KsKsjh;
import com.xcwlkj.identityverify.model.domain.KsKsrcxx;
import com.xcwlkj.identityverify.model.dto.sjscgl.JksjscDTO;
import com.xcwlkj.identityverify.model.dto.sjscgl.JkzpscDTO;
import com.xcwlkj.identityverify.model.dto.sjscgl.KssjscDTO;
import com.xcwlkj.identityverify.model.dto.sjscgl.KszpscDTO;
import com.xcwlkj.identityverify.model.enums.KsjhCjlxEnum;
import com.xcwlkj.identityverify.model.enums.KsrcxxSbztEnum;
import com.xcwlkj.identityverify.model.enums.SfhySftgEnum;
import com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient;
import com.xcwlkj.identityverify.provincePlatform.request.SbkkwReq;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SbkkwDTO;
import com.xcwlkj.identityverify.service.JySysDictService;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import com.xcwlkj.identityverify.service.KsKsjhService;
import com.xcwlkj.identityverify.service.KsKsrcxxService;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.identityverify.third.superior.http.service.*;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PlatformDataServiceImpl implements PlatformDataService {
    @Resource
    private JySysDictService jySysDictService;
    @Resource
    private HisomeServer hisomeServer;
    @Resource
    private JsJFService jsJFService;
    @Resource
    private HbTHService hbTHService;
    @Resource
    private KsKsrcxxService ksKsrcxxService;
    @Resource
    private KsKsjhService ksKsjhService;
    @Resource
    private HisomeV1Service hisomeV1Service;
    @Resource
    private KsKkwMsgService ksKkwMsgService;
    @Resource
    private ProvincePlatformClient provincePlatformClient;


    @Override
    public void ksxxUpload(KssjscDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        String tValue = getPlatCode(ksjhbh);

        if (StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            ksKsrcxxService.uploadValidateInfoToProvince(ksjhbh, ccm);
            ksKsrcxxService.uploadManualEntranceToProvince(ksjhbh, ccm);
        } else if (StringUtils.equals(SuperiorPlatEnum.HISOME_V1.getCode(), tValue)) {
            hisomeV1Service.uploadHyxxV1(ksjhbh, ccm);
            hisomeV1Service.uploadRcxxV1(ksjhbh, ccm);
        } else {
            Example example = new Example(KsKsrcxx.class);
            example.createCriteria()
                    .andEqualTo("ksjhbh", ksjhbh)
                    .andEqualTo("ccm", ccm)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                    .andEqualTo("sfrc", SfhySftgEnum.YRC.getCode());
            example.and()
                    .andNotEqualTo("sbzt", KsrcxxSbztEnum.ysb.getCode())
                    .orIsNull("sbzt");
            List<KsKsrcxx> ksKsrcxxes = ksKsrcxxService.selectListByExample(example);
            if (CollectionUtils.isEmpty(ksKsrcxxes)) {
                log.info("计划：{}，场次：{} ，无需要上报的考生入场信息！", ksjhbh, ccm);
                return;
            }
            if (StringUtils.equals(SuperiorPlatEnum.JS_JF.getCode(), tValue)) {
                jsJFService.uploadHyjg(ksjhbh, ccm, ksKsrcxxes);
            } else if (StringUtils.equals(SuperiorPlatEnum.HB_TH.getCode(), tValue)) {
                hbTHService.uploadHyjg(ksjhbh, ccm, ksKsrcxxes);
            }
        }
    }

    @Override
    public void kszpUpload(KszpscDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        String tValue = getPlatCode(ksjhbh);
        if (StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            ksKsrcxxService.uploadCandidatePhotosToProvince(ksjhbh, ccm);
        } else {
            Example example = new Example(KsKsrcxx.class);
            example.createCriteria()
                    .andEqualTo("ksjhbh", ksjhbh)
                    .andEqualTo("ccm", ccm)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                    .andEqualTo("sfrc", SfhySftgEnum.YRC.getCode())
                    .andIsNotNull("rcrlzp")
                    .andIsNotNull("sfzp");
            example.and()
                    .andNotEqualTo("tbzt", KsrcxxSbztEnum.ysb.getCode())
                    .orIsNull("tbzt");
            List<KsKsrcxx> ksKsrcxxes = ksKsrcxxService.selectListByExample(example);
            if (CollectionUtils.isEmpty(ksKsrcxxes)) {
                log.info("计划：{}，场次：{} ，无需要上报的考生照片信息！", ksjhbh, ccm);
                return;
            }
            if (StringUtils.equals(SuperiorPlatEnum.JS_JF.getCode(), tValue)) {
                jsJFService.uploadHyzp(ksjhbh, ccm, ksKsrcxxes);
            } else if (StringUtils.equals(SuperiorPlatEnum.HB_TH.getCode(), tValue)) {
                hbTHService.uploadHyzp(ksjhbh, ccm, ksKsrcxxes);
            }

        }
    }

    @Override
    public void jksjUpload(JksjscDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        String tValue = getPlatCode(ksjhbh);
        if (StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            hisomeServer.uploadJkqdxx(ksjhbh, ccm);
        }
    }

    @Override
    public void jkzpUpload(JkzpscDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        String tValue = getPlatCode(ksjhbh);
        if (StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            hisomeServer.uploadJkzpxx(ksjhbh, ccm);
        }
    }

    @Override
    public void kkwUpload(String ksjhbh, String ccm) {
        log.info("开始上报空考位数据，考试计划编号：{}，场次码：{}", ksjhbh, ccm);

        String tValue = getPlatCode(ksjhbh);

        // 只有HISOME平台支持空考位上报
        if (!StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            log.info("当前平台[{}]不支持空考位上报功能", tValue);
            return;
        }

        try {
            // 查询未上报的空考位数据
            List<KsKkwMsg> unReportedKkwMsgs = getUnReportedKkwMsgs(ksjhbh, ccm);

            if (CollectionUtils.isEmpty(unReportedKkwMsgs)) {
                log.info("计划：{}，场次：{} ，无需要上报的空考位信息！", ksjhbh, ccm);
                return;
            }

            log.info("找到{}条未上报的空考位数据", unReportedKkwMsgs.size());

            // 按设备序列号分组批量上报
            Map<String, List<KsKkwMsg>> groupedByDevice = unReportedKkwMsgs.stream()
                    .collect(Collectors.groupingBy(KsKkwMsg::getSn));

            for (Map.Entry<String, List<KsKkwMsg>> entry : groupedByDevice.entrySet()) {
                String deviceSn = entry.getKey();
                List<KsKkwMsg> deviceKkwMsgs = entry.getValue();

                try {
                    // 转换数据格式
                    SbkkwDTO sbkkwDTO = convertToSbkkwDTO(deviceKkwMsgs);

                    // 构建请求
                    SbkkwReq sbkkwReq = provincePlatformClient.buildSbkkwRequest(sbkkwDTO, deviceSn);

                    // 调用上级平台接口
                    Wrapper<Void> response = provincePlatformClient.sbkkw(sbkkwReq);

                    if (response != null && response.success()) {
                        // 更新上报状态为成功
                        updateReportStatus(deviceKkwMsgs, "1");
                        log.info("设备[{}]空考位数据上报成功，共{}条", deviceSn, deviceKkwMsgs.size());
                    } else {
                        // 更新上报状态为失败
                        String errorMsg = response != null ? response.getMessage() : "未知错误";
                        updateReportStatus(deviceKkwMsgs, "-1");
                        log.error("设备[{}]空考位数据上报失败：{}", deviceSn, errorMsg);
                    }
                } catch (Exception e) {
                    // 更新上报状态为失败
                    updateReportStatus(deviceKkwMsgs, "-1");
                    log.error("设备[{}]空考位数据上报异常", deviceSn, e);
                }
            }

            log.info("空考位数据上报完成，考试计划编号：{}，场次码：{}", ksjhbh, ccm);

        } catch (Exception e) {
            log.error("空考位数据上报异常，考试计划编号：{}，场次码：{}", ksjhbh, ccm, e);
            throw new IdentityVerifyException("空考位数据上报失败：" + e.getMessage());
        }
    }

    private String getPlatCode(String ksjhbh) {
        KsKsjh ksKsjh = ksKsjhService.selectSingleByKey(ksjhbh);

        if (ksKsjh == null) {
            throw new IdentityVerifyException("未查询到相关考试计划!");
        }

        if (!StringUtils.equals(ksKsjh.getCjlx(), KsjhCjlxEnum.SJPTDR.getCode())) {
            throw new IdentityVerifyException("考试计划非上级平台导入，无法上传!");
        }

        JySysDict jySysDict = jySysDictService.queryConfigValueByKey(GlobalKeys.DEFAULT_PLAT);
        if (jySysDict == null) {
            throw new IdentityVerifyException("未设置默认上级平台！");
        }
        return jySysDict.getTValue();
    }

    /**
     * 查询未上报的空考位消息
     */
    private List<KsKkwMsg> getUnReportedKkwMsgs(String ksjhbh, String ccm) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
                .andCondition("(report_flag is null or report_flag = '0' or report_flag = '-1')");

        return ksKkwMsgService.selectListByExample(example);
    }

    /**
     * 将KsKkwMsg列表转换为SbkkwDTO
     */
    private SbkkwDTO convertToSbkkwDTO(List<KsKkwMsg> kkwMsgs) {
        if (CollectionUtils.isEmpty(kkwMsgs)) {
            return null;
        }

        // 按考场分组
        Map<String, List<KsKkwMsg>> groupedByRoom = kkwMsgs.stream()
                .collect(Collectors.groupingBy(msg ->
                    msg.getBzhkcid() + "_" + msg.getLjkch() + "_" + msg.getKch()));

        List<SbkkwDTO.SbkkwDataItem> dataArray = new ArrayList<>();

        // 取第一条记录的基本信息
        KsKkwMsg firstMsg = kkwMsgs.get(0);

        for (Map.Entry<String, List<KsKkwMsg>> roomEntry : groupedByRoom.entrySet()) {
            List<KsKkwMsg> roomMsgs = roomEntry.getValue();
            KsKkwMsg roomFirstMsg = roomMsgs.get(0);

            SbkkwDTO.SbkkwDataItem dataItem = new SbkkwDTO.SbkkwDataItem();
            dataItem.setExamPlanCode(roomFirstMsg.getKsjhbh());
            dataItem.setOrgCode(roomFirstMsg.getBzhkdid());
            dataItem.setSn(roomFirstMsg.getSn());
            dataItem.setDevType(roomFirstMsg.getDevType());
            dataItem.setCcm(roomFirstMsg.getCcm());
            dataItem.setBzhkcid(roomFirstMsg.getBzhkcid());
            dataItem.setZcqswzm(roomFirstMsg.getZcqswzm());
            dataItem.setZwbjfsm(roomFirstMsg.getZwbjfsm());
            dataItem.setZwplfsm(roomFirstMsg.getZwplfsm());

            // 构建考位列表
            List<SbkkwDTO.KwlbItem> kwlbList = new ArrayList<>();
            SbkkwDTO.KwlbItem kwlbItem = new SbkkwDTO.KwlbItem();
            kwlbItem.setLjkch(roomFirstMsg.getLjkch());
            kwlbItem.setKch(roomFirstMsg.getKch());

            // 构建考生位号详情
            List<SbkkwDTO.KzwhxqItem> kzwhxqList = new ArrayList<>();
            for (KsKkwMsg msg : roomMsgs) {
                SbkkwDTO.KzwhxqItem kzwhxqItem = new SbkkwDTO.KzwhxqItem();
                kzwhxqItem.setZkzh(msg.getKsZkzh());
                kzwhxqItem.setBpzwh(msg.getKsBpzwh());
                kzwhxqItem.setSjzwh(msg.getKsSjzwh());
                kzwhxqItem.setRcbz(msg.getRcbz());
                kzwhxqItem.setSjyxj(msg.getSjyxj() != null ? msg.getSjyxj().toString() : "20");

                // 构建已上报核验信息（如果有的话）
                if (StringUtils.isNotBlank(msg.getYzfs()) || StringUtils.isNotBlank(msg.getYzjg()) || StringUtils.isNotBlank(msg.getSfrc())) {
                    SbkkwDTO.YsbhyxxItem ysbhyxxItem = new SbkkwDTO.YsbhyxxItem();

                    // 设置验证时间
                    if (StringUtils.isNotBlank(msg.getRcsj()) && !"-".equals(msg.getRcsj())) {
                        ysbhyxxItem.setYzsj(msg.getRcsj());
                    } else if (msg.getCzsj() != null) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        ysbhyxxItem.setYzsj(sdf.format(msg.getCzsj()));
                    }

                    // 设置核验结果
                    ysbhyxxItem.setHyjg(StringUtils.equals(msg.getSfrc(), SfhySftgEnum.TG.getCode()) ? "1" : "0");

                    // 设置考点代码
                    ysbhyxxItem.setKddm(msg.getBzhkdid());

                    // 解析验证结果，设置各项验证结果
                    String yzjg = msg.getYzjg();
                    if (StringUtils.isNotBlank(yzjg)) {
                        // 根据验证方式和验证结果设置各项结果
                        String yzfs = msg.getYzfs();
                        if (StringUtils.isNotBlank(yzfs)) {
                            // 默认都设置为不通过
                            ysbhyxxItem.setSzjg("0");
                            ysbhyxxItem.setRlsbjg("0");
                            ysbhyxxItem.setZwrzjg("0");

                            // 根据验证方式和结果设置对应的值
                            for (int i = 0; i < yzfs.length() && i < yzjg.length(); i++) {
                                char fs = yzfs.charAt(i);
                                char jg = yzjg.charAt(i);
                                String result = String.valueOf(jg);

                                switch (fs) {
                                    case '1': // 身份证验证
                                        ysbhyxxItem.setSzjg(result);
                                        break;
                                    case '2': // 人脸识别
                                        ysbhyxxItem.setRlsbjg(result);
                                        break;
                                    case '3': // 指纹认证
                                        ysbhyxxItem.setZwrzjg(result);
                                        break;
                                }
                            }
                        }
                    }

                    kzwhxqItem.setYsbhyxx(ysbhyxxItem);
                }

                // 构建已上报入场信息（如果有的话）
                if (StringUtils.isNotBlank(msg.getRgyzjg())) {
                    SbkkwDTO.YsbrcxxItem ysbrcxxItem = new SbkkwDTO.YsbrcxxItem();

                    // 设置上报时间
                    if (msg.getReportTime() != null) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        ysbrcxxItem.setSbsj(sdf.format(msg.getReportTime()));
                    } else if (msg.getCzsj() != null) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        ysbrcxxItem.setSbsj(sdf.format(msg.getCzsj()));
                    }

                    // 设置考点代码
                    ysbrcxxItem.setKddm(msg.getBzhkdid());

                    // 设置入场状态
                    ysbrcxxItem.setRczt(msg.getRgyzjg());

                    kzwhxqItem.setYsbrcxx(ysbrcxxItem);
                }

                kzwhxqList.add(kzwhxqItem);
            }

            kwlbItem.setKzwhxq(kzwhxqList);
            kwlbList.add(kwlbItem);
            dataItem.setKwlb(kwlbList);

            dataArray.add(dataItem);
        }

        SbkkwDTO sbkkwDTO = new SbkkwDTO();
        sbkkwDTO.setDataArray(dataArray);

        // 设置操作时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sbkkwDTO.setCzsj(sdf.format(new Date()));
        sbkkwDTO.setTimestamp(System.currentTimeMillis());

        return sbkkwDTO;
    }

    /**
     * 更新上报状态
     */
    private void updateReportStatus(List<KsKkwMsg> kkwMsgs, String reportFlag) {
        KsKkwMsg ksKkwMsg = new KsKkwMsg();
        ksKkwMsg.setReportFlag(reportFlag);
        ksKkwMsg.setReportTime(new Date());
        ksKkwMsg.setUpdateTime(new Date());

        List<String> ids = kkwMsgs.stream()
                .map(KsKkwMsg::getId)
                .collect(Collectors.toList());
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                        .andIn("id", ids)
                        .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        ksKkwMsgService.updateByExampleSelective(ksKkwMsg, example);

        if ("1".equals(reportFlag)) {
            log.info("更新{}条空考位消息上报状态为成功", kkwMsgs.size());
        } else {
            log.warn("更新{}条空考位消息上报状态为失败", kkwMsgs.size());
        }
    }
}
