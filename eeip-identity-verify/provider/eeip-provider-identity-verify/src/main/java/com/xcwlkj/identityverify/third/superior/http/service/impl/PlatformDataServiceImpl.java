package com.xcwlkj.identityverify.third.superior.http.service.impl;

import com.xcwlkj.exceptions.IdentityVerifyException;
import com.xcwlkj.identityverify.model.constant.GlobalKeys;
import com.xcwlkj.identityverify.model.domain.JySysDict;
import com.xcwlkj.identityverify.model.domain.KsKkwMsg;
import com.xcwlkj.identityverify.model.domain.KsKsjh;
import com.xcwlkj.identityverify.model.domain.KsKsrcxx;
import com.xcwlkj.identityverify.model.dto.sjscgl.*;
import com.xcwlkj.identityverify.model.enums.KsjhCjlxEnum;
import com.xcwlkj.identityverify.model.enums.KsrcxxSbztEnum;
import com.xcwlkj.identityverify.model.enums.SfhySftgEnum;
import com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient;
import com.xcwlkj.identityverify.provincePlatform.request.SbkkwReq;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SbkkwDTO;
import com.xcwlkj.identityverify.service.JySysDictService;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import com.xcwlkj.identityverify.service.KsKsjhService;
import com.xcwlkj.identityverify.service.KsKsrcxxService;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.identityverify.third.superior.http.service.*;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PlatformDataServiceImpl implements PlatformDataService {
    @Resource
    private JySysDictService jySysDictService;
    @Resource
    private HisomeServer hisomeServer;
    @Resource
    private JsJFService jsJFService;
    @Resource
    private HbTHService hbTHService;
    @Resource
    private KsKsrcxxService ksKsrcxxService;
    @Resource
    private KsKsjhService ksKsjhService;
    @Resource
    private HisomeV1Service hisomeV1Service;


    @Override
    public void ksxxUpload(KssjscDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        String tValue = getPlatCode(ksjhbh);

        if (StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            ksKsrcxxService.uploadValidateInfoToProvince(ksjhbh, ccm);
            ksKsrcxxService.uploadManualEntranceToProvince(ksjhbh, ccm);
        } else if (StringUtils.equals(SuperiorPlatEnum.HISOME_V1.getCode(), tValue)) {
            hisomeV1Service.uploadHyxxV1(ksjhbh, ccm);
            hisomeV1Service.uploadRcxxV1(ksjhbh, ccm);
        } else {
            Example example = new Example(KsKsrcxx.class);
            example.createCriteria()
                    .andEqualTo("ksjhbh", ksjhbh)
                    .andEqualTo("ccm", ccm)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                    .andEqualTo("sfrc", SfhySftgEnum.YRC.getCode());
            example.and()
                    .andNotEqualTo("sbzt", KsrcxxSbztEnum.ysb.getCode())
                    .orIsNull("sbzt");
            List<KsKsrcxx> ksKsrcxxes = ksKsrcxxService.selectListByExample(example);
            if (CollectionUtils.isEmpty(ksKsrcxxes)) {
                log.info("计划：{}，场次：{} ，无需要上报的考生入场信息！", ksjhbh, ccm);
                return;
            }
            if (StringUtils.equals(SuperiorPlatEnum.JS_JF.getCode(), tValue)) {
                jsJFService.uploadHyjg(ksjhbh, ccm, ksKsrcxxes);
            } else if (StringUtils.equals(SuperiorPlatEnum.HB_TH.getCode(), tValue)) {
                hbTHService.uploadHyjg(ksjhbh, ccm, ksKsrcxxes);
            }
        }
    }

    @Override
    public void kszpUpload(KszpscDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        String tValue = getPlatCode(ksjhbh);
        if (StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            ksKsrcxxService.uploadCandidatePhotosToProvince(ksjhbh, ccm);
        } else {
            Example example = new Example(KsKsrcxx.class);
            example.createCriteria()
                    .andEqualTo("ksjhbh", ksjhbh)
                    .andEqualTo("ccm", ccm)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                    .andEqualTo("sfrc", SfhySftgEnum.YRC.getCode())
                    .andIsNotNull("rcrlzp")
                    .andIsNotNull("sfzp");
            example.and()
                    .andNotEqualTo("tbzt", KsrcxxSbztEnum.ysb.getCode())
                    .orIsNull("tbzt");
            List<KsKsrcxx> ksKsrcxxes = ksKsrcxxService.selectListByExample(example);
            if (CollectionUtils.isEmpty(ksKsrcxxes)) {
                log.info("计划：{}，场次：{} ，无需要上报的考生照片信息！", ksjhbh, ccm);
                return;
            }
            if (StringUtils.equals(SuperiorPlatEnum.JS_JF.getCode(), tValue)) {
                jsJFService.uploadHyzp(ksjhbh, ccm, ksKsrcxxes);
            } else if (StringUtils.equals(SuperiorPlatEnum.HB_TH.getCode(), tValue)) {
                hbTHService.uploadHyzp(ksjhbh, ccm, ksKsrcxxes);
            }

        }
    }

    @Override
    public void jksjUpload(JksjscDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        String tValue = getPlatCode(ksjhbh);
        if (StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            hisomeServer.uploadJkqdxx(ksjhbh, ccm);
        }
    }

    @Override
    public void jkzpUpload(JkzpscDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        String tValue = getPlatCode(ksjhbh);
        if (StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            hisomeServer.uploadJkzpxx(ksjhbh, ccm);
        }
    }

    @Override
    public void kkwUpload(SckkwDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        String tValue = getPlatCode(ksjhbh);

        // 只有HISOME平台支持空考位上报
        if (!StringUtils.equals(SuperiorPlatEnum.HISOME.getCode(), tValue)) {
            log.info("当前平台[{}]不支持空考位上报功能", tValue);
            return;
        }
        hisomeServer.uploadKkwxx(ksjhbh, ccm);
    }

    private String getPlatCode(String ksjhbh) {
        KsKsjh ksKsjh = ksKsjhService.selectSingleByKey(ksjhbh);

        if (ksKsjh == null) {
            throw new IdentityVerifyException("未查询到相关考试计划!");
        }

        if (!StringUtils.equals(ksKsjh.getCjlx(), KsjhCjlxEnum.SJPTDR.getCode())) {
            throw new IdentityVerifyException("考试计划非上级平台导入，无法上传!");
        }

        JySysDict jySysDict = jySysDictService.queryConfigValueByKey(GlobalKeys.DEFAULT_PLAT);
        if (jySysDict == null) {
            throw new IdentityVerifyException("未设置默认上级平台！");
        }
        return jySysDict.getTValue();
    }
}
