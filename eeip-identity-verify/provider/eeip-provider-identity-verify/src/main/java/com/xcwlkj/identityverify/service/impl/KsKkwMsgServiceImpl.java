/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwTjDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwXqDTO;
import com.xcwlkj.identityverify.model.enums.KkwYclxEnum;
import com.xcwlkj.identityverify.model.enums.SfhySftgEnum;
import com.xcwlkj.identityverify.model.resp.cxtj.KkwTjRespModel;
import com.xcwlkj.identityverify.model.resp.cxtj.KkwXqRespModel;
import com.xcwlkj.identityverify.model.vo.attachment.UploadAttachmentReturnUrlVO;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjInfoVO;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjItem;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjXqVO;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SbkkwDTO;
import com.xcwlkj.identityverify.template.KkwXqTemplate;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.xcwlkj.identityverify.model.domain.KsKkwMsg;

import com.xcwlkj.identityverify.mapper.KsKkwMsgMapper;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 空考位消息服务实现
 * <AUTHOR>
 * @version $Id: KsKkwMsgServiceImpl.java, v 0.1 2025年07月28日 xcwlkj.com Exp $
 */
@Slf4j
@Service("ksKkwMsgService")
public class KsKkwMsgServiceImpl extends BaseServiceImpl<KsKkwMsgMapper, KsKkwMsg> implements KsKkwMsgService {

    @Resource
    private KsKkwMsgMapper modelMapper;
    @Value("${xc.temp.generatePath}")
    private String generatePath;
    @Resource
    private AttachmentHandler attachmentHandler;
    @Override
    public List<KsKkwMsg> findByKsjhbhAndCcm(String ksjhbh, String ccm) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public List<KsKkwMsg> findByKsjhbhAndCcmAndKch(String ksjhbh, String ccm, String kch) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("kch", kch)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public void insertKkwMsg(KsKkwMsg ksKkwMsg) {
        if (StringUtil.isEmpty(ksKkwMsg.getId())) {
            ksKkwMsg.setId(IdGenerateUtil.generateId());
        }
        if (ksKkwMsg.getCreateTime() == null) {
            ksKkwMsg.setCreateTime(new Date());
        }
        if (ksKkwMsg.getUpdateTime() == null) {
            ksKkwMsg.setUpdateTime(new Date());
        }
        if (StringUtil.isEmpty(ksKkwMsg.getSczt())) {
            ksKkwMsg.setSczt(ScztEnum.NOTDEL.getCode());
        }
        modelMapper.insertSelective(ksKkwMsg);
    }

    @Override
    public List<KsKkwMsg> findBySn(String sn) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("sn", sn)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public List<KsKkwMsg> findByZkzh(String zkzh) {
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andEqualTo("ksZkzh", zkzh)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        return modelMapper.selectByExample(example);
    }

    @Override
    public KkwTjRespModel getKkwTj(KkwTjDTO dto) {
        // 查询统计摘要信息
        KkwTjInfoVO summary = modelMapper.selectKkwTjInfo(dto);

        // 分页查询列表数据
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<KkwTjItem> list = modelMapper.selectKkwTjLb(dto);
        PageInfo<KkwTjItem> pageInfo = new PageInfo<>(list);

        KkwTjRespModel respModel = new KkwTjRespModel();
        respModel.setPageInfo(pageInfo);
        respModel.setSummary(summary);

        log.info("空考位统计查询完成，总记录数：{}，摘要信息：{}", pageInfo.getTotal(), summary);
        return respModel;
    }

    @Override
    public KkwXqRespModel getKkwTjXq(KkwXqDTO dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<KkwTjXqVO> list = modelMapper.selectKkwTjXq(dto);
        PageInfo<KkwTjXqVO> pageInfo = new PageInfo<>(list);

        // 构建响应对象
        KkwXqRespModel respModel = new KkwXqRespModel();
        respModel.setPageInfo(pageInfo);

        log.info("空考位详情查询完成，总记录数：{}", pageInfo.getTotal());
        return respModel;
    }

    @Override
    public String exportKkwXqExcel(KkwXqDTO dto) {
        log.info("开始导出空考位详情Excel，查询条件：{}", dto);

        // 查询所有数据，不分页
        List<KkwTjXqVO> dataList = modelMapper.selectKkwTjXq(dto);

        // 数据转换为Excel模板对象
        List<KkwXqTemplate> templateList = new ArrayList<>();
        for (KkwTjXqVO vo : dataList) {
            KkwXqTemplate template = new KkwXqTemplate();
            template.setKch(vo.getKch());
            template.setCsmc(vo.getCsmc());
            template.setZkzh(vo.getZkzh());
            template.setXm(vo.getXm());
            template.setZwh(vo.getZwh());
            template.setYclx(KkwYclxEnum.getDescriptionByCode(vo.getYclx()));
            template.setZdsbsj(vo.getZdsbsj());
            // 添加逻辑考场号（使用考场号作为逻辑考场号）
            template.setLjkcbh(vo.getLjkcbh());
            templateList.add(template);
        }


        String rootPath = generatePath + File.separator + "kkw" + File.separator;
        File rootDir = new File(rootPath);
        if (!rootDir.exists()) {
            rootDir.mkdirs();
        }
        // 生成文件名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String fileName = "空考位详情_" + timestamp + ".xlsx";

        String filePath = rootPath + fileName;

        // 使用EasyExcel写入文件
        EasyExcel.write(filePath, KkwXqTemplate.class)
                .sheet("空考位详情")
                .doWrite(templateList);
        Date expiteTime = DateUtil.offsetHour(DateUtil.getCurrentDT(), 2);
        UploadAttachmentReturnUrlVO uploadAttachmentReturnUrlVO = attachmentHandler.uploadAttachmentReturnUrl(filePath, expiteTime, null);
        rootDir.delete();

        return uploadAttachmentReturnUrlVO.getAttachmentUrl();
    }

    @Override
    public List<KsKkwMsg> findUnReportedKkwMsgs(String ksjhbh, String ccm) {
        List<KsKkwMsg> result = modelMapper.selectUnReportedKkwMsgs(ksjhbh, ccm);
        log.info("查询未上报空考位数据，考试计划：{}，场次：{}，找到{}条记录", ksjhbh, ccm, result.size());
        return result;
    }

    @Override
    public Map<String, SbkkwDTO> convertToSbkkwDTOMap(List<KsKkwMsg> kkwMsgs) {
        if (CollectionUtils.isEmpty(kkwMsgs)) {
            return new HashMap<>();
        }

        log.info("开始转换{}条空考位数据为上报格式", kkwMsgs.size());

        // 按设备序列号分组
        Map<String, List<KsKkwMsg>> groupedBySn = kkwMsgs.stream()
                .collect(Collectors.groupingBy(KsKkwMsg::getSn));

        Map<String, SbkkwDTO> result = groupedBySn.entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> convertToSbkkwDTO(entry.getValue())
                ));

        log.info("转换完成，按{}个设备分组", result.size());
        return result;
    }

    /**
     * 将KsKkwMsg列表转换为SbkkwDTO
     */
    private SbkkwDTO convertToSbkkwDTO(List<KsKkwMsg> kkwMsgs) {
        if (CollectionUtils.isEmpty(kkwMsgs)) {
            return null;
        }

        // 按考场分组
        Map<String, List<KsKkwMsg>> groupedByRoom = kkwMsgs.stream()
                .collect(Collectors.groupingBy(msg ->
                    msg.getBzhkcid() + "_" + msg.getLjkch() + "_" + msg.getKch()));

        List<SbkkwDTO.SbkkwDataItem> dataArray = new ArrayList<>();


        for (Map.Entry<String, List<KsKkwMsg>> roomEntry : groupedByRoom.entrySet()) {
            List<KsKkwMsg> roomMsgs = roomEntry.getValue();
            KsKkwMsg roomFirstMsg = roomMsgs.get(0);

            SbkkwDTO.SbkkwDataItem dataItem = new SbkkwDTO.SbkkwDataItem();
            dataItem.setExamPlanCode(roomFirstMsg.getKsjhbh());
            dataItem.setOrgCode(roomFirstMsg.getBzhkdid());
            dataItem.setSn(roomFirstMsg.getSn());
            dataItem.setDevType(roomFirstMsg.getDevType());
            dataItem.setCcm(roomFirstMsg.getCcm());
            dataItem.setBzhkcid(roomFirstMsg.getBzhkcid());
            dataItem.setZcqswzm(roomFirstMsg.getZcqswzm());
            dataItem.setZwbjfsm(roomFirstMsg.getZwbjfsm());
            dataItem.setZwplfsm(roomFirstMsg.getZwplfsm());

            // 构建考位列表
            List<SbkkwDTO.KwlbItem> kwlbList = new ArrayList<>();
            SbkkwDTO.KwlbItem kwlbItem = new SbkkwDTO.KwlbItem();
            kwlbItem.setLjkch(roomFirstMsg.getLjkch());
            kwlbItem.setKch(roomFirstMsg.getKch());

            // 构建考生位号详情
            List<SbkkwDTO.KzwhxqItem> kzwhxqList = new ArrayList<>();
            for (KsKkwMsg msg : roomMsgs) {
                SbkkwDTO.KzwhxqItem kzwhxqItem = new SbkkwDTO.KzwhxqItem();
                kzwhxqItem.setZkzh(msg.getKsZkzh());
                kzwhxqItem.setBpzwh(msg.getKsBpzwh());
                kzwhxqItem.setSjzwh(msg.getKsSjzwh());
                kzwhxqItem.setRcbz(msg.getRcbz());
                kzwhxqItem.setSjyxj(msg.getSjyxj() != null ? msg.getSjyxj().toString() : "20");

                // 构建已上报核验信息
                setYsbhyxx(msg, kzwhxqItem);

                // 构建已上报入场信息
                setYsbrcxx(msg, kzwhxqItem);

                kzwhxqList.add(kzwhxqItem);
            }

            kwlbItem.setKzwhxq(kzwhxqList);
            kwlbList.add(kwlbItem);
            dataItem.setKwlb(kwlbList);

            dataArray.add(dataItem);
        }

        SbkkwDTO sbkkwDTO = new SbkkwDTO();
        sbkkwDTO.setDataArray(dataArray);

        // 设置操作时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sbkkwDTO.setCzsj(sdf.format(new Date()));
        sbkkwDTO.setTimestamp(System.currentTimeMillis());

        return sbkkwDTO;
    }

    @Override
    public void batchUpdateReportStatus(List<KsKkwMsg> kkwMsgs, String reportFlag, String errorMsg) {
        if (CollectionUtils.isEmpty(kkwMsgs)) {
            return;
        }
        List<String> ids = kkwMsgs.stream().map(KsKkwMsg::getId).collect(Collectors.toList());
        Date now = new Date();
        KsKkwMsg ksKkwMsg = new KsKkwMsg();
        ksKkwMsg.setReportFlag(reportFlag);
        ksKkwMsg.setReportTime(now);
        ksKkwMsg.setUpdateTime(now);
        Example example = new Example(KsKkwMsg.class);
        example.createCriteria()
                .andIn("id", ids)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        this.modelMapper.updateByExampleSelective(ksKkwMsg, example);

        if ("1".equals(reportFlag)) {
            log.info("批量更新{}条空考位消息上报状态为成功", kkwMsgs.size());
        } else {
            log.warn("批量更新{}条空考位消息上报状态为失败", kkwMsgs.size());
        }
    }

    /**
     * 设置已上报核验信息
     */
    private void setYsbhyxx(KsKkwMsg msg, SbkkwDTO.KzwhxqItem kzwhxqItem) {
        if (StringUtils.isNotBlank(msg.getYzfs()) || StringUtils.isNotBlank(msg.getYzjg()) || StringUtils.isNotBlank(msg.getSfrc())) {
            SbkkwDTO.YsbhyxxItem ysbhyxxItem = new SbkkwDTO.YsbhyxxItem();

            // 设置验证时间
            if (StringUtils.isNotBlank(msg.getRcsj()) && !"-".equals(msg.getRcsj())) {
                ysbhyxxItem.setYzsj(msg.getRcsj());
            } else if (msg.getCzsj() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                ysbhyxxItem.setYzsj(sdf.format(msg.getCzsj()));
            }

            // 设置核验结果
            ysbhyxxItem.setHyjg(StringUtils.equals(msg.getSfrc(), SfhySftgEnum.TG.getCode()) ? "1" : "0");

            // 设置考点代码
            ysbhyxxItem.setKddm(msg.getBzhkdid());

            // 解析验证结果，设置各项验证结果
            String yzjg = msg.getYzjg();
            if (StringUtils.isNotBlank(yzjg)) {
                // 根据验证方式和验证结果设置各项结果
                String yzfs = msg.getYzfs();
                if (StringUtils.isNotBlank(yzfs)) {
                    // 默认都设置为不通过
                    ysbhyxxItem.setSzjg("0");
                    ysbhyxxItem.setRlsbjg("0");
                    ysbhyxxItem.setZwrzjg("0");

                    // 根据验证方式和结果设置对应的值
                    for (int i = 0; i < yzfs.length() && i < yzjg.length(); i++) {
                        char fs = yzfs.charAt(i);
                        char jg = yzjg.charAt(i);
                        String result = String.valueOf(jg);

                        switch (fs) {
                            case '1': // 身份证验证
                                ysbhyxxItem.setSzjg(result);
                                break;
                            case '2': // 人脸识别
                                ysbhyxxItem.setRlsbjg(result);
                                break;
                            case '3': // 指纹认证
                                ysbhyxxItem.setZwrzjg(result);
                                break;
                        }
                    }
                }
            }

            kzwhxqItem.setYsbhyxx(ysbhyxxItem);
        }
    }

    /**
     * 设置已上报入场信息
     */
    private void setYsbrcxx(KsKkwMsg msg, SbkkwDTO.KzwhxqItem kzwhxqItem) {
        if (StringUtils.isNotBlank(msg.getRgyzjg())) {
            SbkkwDTO.YsbrcxxItem ysbrcxxItem = new SbkkwDTO.YsbrcxxItem();

            // 设置上报时间
            if (msg.getReportTime() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                ysbrcxxItem.setSbsj(sdf.format(msg.getReportTime()));
            } else if (msg.getCzsj() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                ysbrcxxItem.setSbsj(sdf.format(msg.getCzsj()));
            }

            // 设置考点代码
            ysbrcxxItem.setKddm(msg.getBzhkdid());

            // 设置入场状态
            ysbrcxxItem.setRczt(msg.getSfrc());

            kzwhxqItem.setYsbrcxx(ysbrcxxItem);
        }
    }
}
