package com.xcwlkj.identityverify.third.superior.http.service;

import com.xcwlkj.identityverify.model.vo.ksgl.SjptksjhlbVO;

/**
 * 浙江核验平台接口
 */
public interface HisomeServer {
    // 考试计划信息导入
    void importData(String planCode);

    //获取考试计划列表
    SjptksjhlbVO getExamPlanList();

    /**
     * 上传监考签到信息
     */
    void uploadJkqdxx(String ksjhbh, String ccm);

    /**
     * 上传监考照片信息
     */
    void uploadJkzpxx(String ksjhbh, String ccm);

    /**
     * 上传空考位信息
     * @param ksjhbh
     * @param ccm
     */
    void uploadKkwxx(String ksjhbh, String ccm);
}
