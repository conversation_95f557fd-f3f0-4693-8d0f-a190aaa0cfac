package com.xcwlkj.identityverify.third.superior.http.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xcwlkj.exceptions.IdentityVerifyException;
import com.xcwlkj.identityverify.handler.UpperHsDfsHandler;
import com.xcwlkj.identityverify.model.domain.*;
import com.xcwlkj.identityverify.model.dos.*;
import com.xcwlkj.identityverify.model.enums.*;
import com.xcwlkj.identityverify.model.vo.ksgl.SjptksjhItemVO;
import com.xcwlkj.identityverify.model.vo.ksgl.SjptksjhlbVO;
import com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient;
import com.xcwlkj.identityverify.provincePlatform.request.*;
import com.xcwlkj.identityverify.provincePlatform.request.dto.JkqdzpDto;
import com.xcwlkj.identityverify.provincePlatform.request.dto.JkryQdDto;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SbkkwDTO;
import com.xcwlkj.identityverify.provincePlatform.request.items.*;
import com.xcwlkj.identityverify.provincePlatform.response.ExamArrgInfoResp;
import com.xcwlkj.identityverify.provincePlatform.response.items.ExamPlanVO;
import com.xcwlkj.identityverify.service.*;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.identityverify.third.superior.http.service.HisomeServer;
import com.xcwlkj.identityverify.util.*;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("hisomeServer")
public class HisomeServerImpl extends XxdrService implements HisomeServer {

    @Resource
    private CsXxjbxxService xxjbxxService;
    @Resource
    private KsKssjPkgStatusService ksKssjPkgStatusService;
    @Resource
    private KsKdxxService ksKdxxService;
    @Resource
    private KsKssjPkgFileService ksKssjPkgFileService;
    @Resource
    private ProvincePlatformClient provincePlatformClient;
    @Resource
    private SbSbxxService sbxxService;
    @Resource
    private KsJkryRcxxService ksJkryRcxxService;
    @Resource
    private UpperHsDfsHandler upperHsDfsHandler;
    @Resource
    private KsKkwMsgService ksKkwMsgService;

    @Override
    public void importData(String planCode) {
        //初始化任务
        KsKssjImportTask ksKssjImportTask = importTaskInit(planCode, ImportCatalogEnum.SJPTDR.getCode(), null);
        KsProcessPeriod ksProcessPeriod = sjtbPeriodInit(planCode);
        KsProcessEvent ksProcessEvent = sjtbEventInit(planCode, DataSynchTypeEnum.SJKSXX);

        //开始任务
        ksKssjImportTask.setComplete(KssjImportTaskCompleteEnum.RUNNING.getValue());
        ksKssjImportTask.setTProgress("20");
        importTaskUpdate(ksKssjImportTask);
        ksProcessPeriod.setPeriodStatus(PeriodStatusEnum.DOING.getCode());
        updatePeriod(ksProcessPeriod);

        //获取数据
        CsXxjbxx xxjbxx = xxjbxxService.selectAll().get(0);
        try {
            //获取并保存考试信息
            int tConf = parseAndSaveExamInfo(planCode, xxjbxx);
            ksKssjImportTask.setTProgress("40");
            ksKssjImportTask.setTConf(tConf);
            importTaskUpdate(ksKssjImportTask);

        } catch (Exception e) {
            setImportTaskAndProcessFail(ksKssjImportTask, ksProcessPeriod, ksProcessEvent, planCode, e, SuperiorPlatEnum.HISOME.getName());
            return;
        }

        //设置任务状态为完成
        setImportTaskAndProcessSuccess(ksKssjImportTask, ksProcessPeriod, ksProcessEvent, SuperiorPlatEnum.HISOME.getName());
        pkgDownloadComplete(planCode, ImportCatalogEnum.SJPTDR, "1");
    }

    @Override
    public SjptksjhlbVO getExamPlanList() {
        SjptksjhlbVO result = new SjptksjhlbVO();
        List<SjptksjhItemVO> ksjhItemVOS = new ArrayList<>();
        ExamPlanReq examPlanReq = new ExamPlanReq();
        List<ExamPlanVO> examPlanResp = provincePlatformClient.examPlan(examPlanReq).getResult();

        for (ExamPlanVO examPlanItem : examPlanResp) {
            SjptksjhItemVO itemVO = new SjptksjhItemVO();
            itemVO.setKsjhbh(examPlanItem.getKsbh());
            itemVO.setKsjhcm(examPlanItem.getKsmc());
            ksjhItemVOS.add(itemVO);
        }
        result.setSjptksjhList(ksjhItemVOS);
        return result;
    }

    /**
     * 导入考试信息
     *
     * @param ksjhbh
     * @param xxjbxx
     * @return
     */
    private int parseAndSaveExamInfo(String ksjhbh, CsXxjbxx xxjbxx) {
        int tConf = 0;
        BaseDTO<DataDTO> baseDTO = new BaseDTO<>();
        DataDTO dataDTO = new DataDTO();
        dataDTO.setExamPlanCode(ksjhbh);
        dataDTO.setOrgCode(xxjbxx.getZzjgm());
        baseDTO.setData(dataDTO);
        Wrapper<ExamArrgInfoResp> response = provincePlatformClient.examArrgInfo(baseDTO);
        if (response == null || response.getResult() == null) {
            throw new IdentityVerifyException("获取考试计划" + ksjhbh + "文件地址失败: " + response.getMessage());
        }
        SjbVersionDO sjbVersionDO = new SjbVersionDO();
        List<KsKssjImportFile> importFileList = new LinkedList<>();
        KsKssjImportStatus importStatus = importStatusInit(ksjhbh, ImportCatalogEnum.SJPTDR.getCode(), xxjbxx.getZzjgm(), xxjbxx.getXxmc());
        // 打包记录
        KsKssjPkgStatus pkgStatus;
        Boolean pkgStatusFlag;
        Example pkgStatusExam = new Example(KsKssjPkgStatus.class);
        pkgStatusExam.createCriteria().andEqualTo("ksjhbh", ksjhbh);
        List<KsKssjPkgStatus> kssjPkgStatuses = ksKssjPkgStatusService.selectListByExample(pkgStatusExam);
        if (kssjPkgStatuses != null && kssjPkgStatuses.size() != 0) {
            pkgStatus = kssjPkgStatuses.get(0);
            pkgStatusFlag = true;
        } else {
            pkgStatusFlag = false;
            pkgStatus = new KsKssjPkgStatus();
            pkgStatus.setKsjhbh(ksjhbh);
            pkgStatus.setId(IdGenerateUtil.generateId());
            pkgStatus.setPkgCatalog(PkgCatalogEnum.KD.getCode());
            pkgStatus.setCreateTime(new Date());
            pkgStatus.setKssjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            pkgStatus.setJkryjbsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            pkgStatus.setJkrybpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            pkgStatus.setJkryzpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            pkgStatus.setKszpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            pkgStatus.setPzsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
        }
        pkgStatus.setUpdateTime(new Date());

        List<KsKssjImportFile> dbFileList = getFileList(importStatus);
        // 获得导入文件数据
        KsProcessPeriod period = sjtbPeriodInit(ksjhbh);
        period.setPeriodStatus(PeriodStatusEnum.DOING.getCode());
        updatePeriod(period);

        ExamArrgInfoResp result = response.getResult();
        String key = result.getSecret().getKey();
        List<FileListItemDO> fileList = result.getFileList();
        for (FileListItemDO file : fileList) {
            addFileImportList(ksjhbh, file, sjbVersionDO, importFileList, dbFileList, importStatus);
        }
        List<KsKssjPkgFile> pkgFileList = new ArrayList<>();
        // 顺序处理数据包

        try {
            KsKssjPkgFile pzPkgFile = sjbCl(importFileList, ImportTypeEnum.PZ, key, pkgStatus, importStatus, sjbVersionDO);
            if (pzPkgFile != null) {
                pkgFileList.add(pzPkgFile);
                tConf += KssjImportTaskTConfEnum.PZXX.getValue();
            }
            KsKssjPkgFile ksPkgFile = sjbCl(importFileList, ImportTypeEnum.KS, key, pkgStatus, importStatus, null);
            if (ksPkgFile != null) {
                pkgFileList.add(ksPkgFile);
                tConf += KssjImportTaskTConfEnum.KS.getValue();
            }
            KsKssjPkgFile kszpPkgFile = sjbCl(importFileList, ImportTypeEnum.KSZP, key, pkgStatus, importStatus, null);
            if (kszpPkgFile != null) {
                pkgFileList.add(kszpPkgFile);
                tConf += KssjImportTaskTConfEnum.KSZP.getValue();
            }
            KsKssjPkgFile jkryPkgFile = sjbCl(importFileList, ImportTypeEnum.JKRY, key, pkgStatus, importStatus, null);
            if (jkryPkgFile != null) {
                pkgFileList.add(jkryPkgFile);
                tConf += KssjImportTaskTConfEnum.JKRYJB.getValue();
            }
            KsKssjPkgFile jkbpPkgFile = sjbCl(importFileList, ImportTypeEnum.JKBP, key, pkgStatus, importStatus, null);
            if (jkbpPkgFile != null) {
                pkgFileList.add(jkbpPkgFile);
                tConf += KssjImportTaskTConfEnum.JKRYBP.getValue();
            }
            KsKssjPkgFile jkzpPkgFile = sjbCl(importFileList, ImportTypeEnum.JKZP, key, pkgStatus, importStatus, null);
            if (jkzpPkgFile != null) {
                pkgFileList.add(jkzpPkgFile);
                tConf += KssjImportTaskTConfEnum.JKRYZP.getValue();
            }
            if (importFileList != null && importFileList.size() != 0)
                fileInsertList(importFileList);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (importFileList.size() == pkgFileList.size()) {
                period.setPeriodStatus(PeriodStatusEnum.DONE.getCode());
                period.setPeriodEndTime(new Date());
            } else {
                period.setPeriodStatus(PeriodStatusEnum.FAIL.getCode());
                period.setPeriodEndTime(new Date());
            }
            updatePeriod(period);
            tbsjDbPeriod(pkgStatus, ksjhbh);
        }

        if (pkgFileList != null && pkgFileList.size() != 0)
            ksKssjPkgFileService.insertListSelective(pkgFileList);
        importFileList.clear();
        importStatusUpdate(importStatus);
        Example kdEx = new Example(KsKdxx.class);
        kdEx.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        List<KsKdxx> kdxxes = ksKdxxService.selectListByExample(kdEx);
        if (StringUtils.isBlank(pkgStatus.getPkgCatalog())) {
            pkgStatus.setPkgCatalog(PkgCatalogEnum.KD.getCode());
        }
        if (kdxxes != null && kdxxes.size() != 0) {
            KsKdxx kdxx = kdxxes.get(0);
            pkgStatus.setBzhkdmc(kdxx.getBzhkdmc());
            pkgStatus.setBzhkdid(kdxx.getBzhkdid());
        }
        pkgStatus.setLatestOperateTime(new Date());
        if (pkgStatusFlag) {
            ksKssjPkgStatusService.updateSingle(pkgStatus);
        } else {
            ksKssjPkgStatusService.insertSingle(pkgStatus);
        }

        return tConf;
    }

    /**
     * 数据包下载情况上报
     *
     * @param ksjhbh
     * @param catalog
     */
    private void pkgDownloadComplete(String ksjhbh, ImportCatalogEnum catalog, String getPkgMode) {
        PkgDownloadCompleteDTO dto = new PkgDownloadCompleteDTO();
        KsKssjImportStatus importStatus = getImportStatus(ksjhbh, catalog);
        List<FileDowndloadStatusItemDTO> fileDowndloadStatusItemDTOS = new ArrayList<>();
        if (SjbQkEnum.NOT_ENABLED.getValue() != importStatus.getKszpsjbQk()) {
            KsKssjImportFile importFile = getImportFileById(importStatus.getFileKszpsjbId());
            FileDowndloadStatusItemDTO itemDTO = new FileDowndloadStatusItemDTO();
            itemDTO.setVersion(importFile.getVersion());
            itemDTO.setType(PackEnum.Pack_GxHisomeKdKsZp.getType());
            itemDTO.setStatus(getDownloadStatus(importStatus.getKszpsjbQk()));
            fileDowndloadStatusItemDTOS.add(itemDTO);
        }
        if (SjbQkEnum.NOT_ENABLED.getValue() != importStatus.getJkryzpsjbQk()) {
            KsKssjImportFile importFile = getImportFileById(importStatus.getFileJkryzpsjbId());
            FileDowndloadStatusItemDTO itemDTO = new FileDowndloadStatusItemDTO();
            itemDTO.setVersion(importFile.getVersion());
            itemDTO.setType(PackEnum.Pack_GxHisomeKdJkryZp.getType());
            itemDTO.setStatus(getDownloadStatus(importStatus.getJkryzpsjbQk()));
            fileDowndloadStatusItemDTOS.add(itemDTO);
        }
        if (SjbQkEnum.NOT_ENABLED.getValue() != importStatus.getKssjbQk()) {
            KsKssjImportFile importFile = getImportFileById(importStatus.getFileKssjbId());
            FileDowndloadStatusItemDTO itemDTO = new FileDowndloadStatusItemDTO();
            itemDTO.setVersion(importFile.getVersion());
            itemDTO.setType(PackEnum.Pack_GxHisomeStu.getType());
            itemDTO.setStatus(getDownloadStatus(importStatus.getKssjbQk()));
            fileDowndloadStatusItemDTOS.add(itemDTO);
        }
        if (SjbQkEnum.NOT_ENABLED.getValue() != importStatus.getJkrybpsjbQk()) {
            KsKssjImportFile importFile = getImportFileById(importStatus.getFileJkrybpsjbId());
            FileDowndloadStatusItemDTO itemDTO = new FileDowndloadStatusItemDTO();
            itemDTO.setVersion(importFile.getVersion());
            itemDTO.setType(PackEnum.Pack_GxHisomeJkryBp.getType());
            itemDTO.setStatus(getDownloadStatus(importStatus.getJkrybpsjbQk()));
            fileDowndloadStatusItemDTOS.add(itemDTO);
        }
        if (SjbQkEnum.NOT_ENABLED.getValue() != importStatus.getJkryjbsjbQk()) {
            KsKssjImportFile importFile = getImportFileById(importStatus.getFileJkryjbsjbId());
            FileDowndloadStatusItemDTO itemDTO = new FileDowndloadStatusItemDTO();
            itemDTO.setVersion(importFile.getVersion());
            itemDTO.setType(PackEnum.Pack_GxHisomeJkry.getType());
            itemDTO.setStatus(getDownloadStatus(importStatus.getJkryjbsjbQk()));
            fileDowndloadStatusItemDTOS.add(itemDTO);
        }
        if (SjbQkEnum.NOT_ENABLED.getValue() != importStatus.getPzsjbQk()) {
            KsKssjImportFile importFile = getImportFileById(importStatus.getFilePzsjbId());
            FileDowndloadStatusItemDTO itemDTO = new FileDowndloadStatusItemDTO();
            itemDTO.setVersion(importFile.getVersion());
            itemDTO.setType(PackEnum.Pack_GxHisomeCommon.getType());
            itemDTO.setStatus(getDownloadStatus(importStatus.getPzsjbQk()));
            fileDowndloadStatusItemDTOS.add(itemDTO);
        }
        String sn = HsUtils.generateComputerIdentifier();
        String sblx = null;
        if (StringUtils.isNotBlank(sn)) {
            Example sbExam = new Example(SbSbxx.class);
            sbExam.createCriteria().andEqualTo("xlh", sn)
                    .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
            sbExam.selectProperties("sblx");
            List<SbSbxx> sbxxes = sbxxService.selectByExample(sbExam);
            SbSbxx sbxx = sbxxes.get(0);
            sblx = JcsblxEnum.getByMsg(sbxx.getSblx()).getCode();
        }
        dto.setFileDowndloadStatusList(fileDowndloadStatusItemDTOS);
        dto.setExamPlanCode(ksjhbh);
        dto.setOrgCode(importStatus.getBzhkdid());
        dto.setGetPkgMode(getPkgMode);
        dto.setSn(sn);
        dto.setDevType(sblx);
        BaseDTO<PkgDownloadCompleteDTO> baseDTO = new BaseDTO<>();
        baseDTO.setData(dto);

        provincePlatformClient.pkgDownloadComplete(baseDTO);
    }

    private String getDownloadStatus(int sjbQk) {
        String result = null;
        switch (sjbQk) {
            case -10:
                result = "-2";
                break;
            case 10:
                result = "2";
                break;
            default:
                result = "1";
        }
        return result;
    }

    @Override
    public void uploadJkqdxx(String ksjhbh, String ccm) {
        Example example = new Example(KsJkryRcxx.class);
        example.createCriteria()
                .andEqualTo("ksjhdm", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("sfrc", SfhySftgEnum.YRC.getCode());
        example.and()
                .andNotEqualTo("sbzt", JkryrcxxSbztEnum.ysb.getCode())
                .orIsNull("sbzt");

        List<KsJkryRcxx> ksJkryRcxxes = ksJkryRcxxService.selectListByExample(example);
        if (CollectionUtils.isEmpty(ksJkryRcxxes)) {
            log.info("考试计划计划：{}，场次：{} ，无需要上报的监考签到信息！", ksjhbh, ccm);
            return;
        }

        Map<String, List<KsJkryRcxx>> map = ksJkryRcxxes.stream().collect(Collectors.groupingBy(item -> StringUtil.isNotBlank(item.getSbxlh()) ? item.getSbxlh() : "DEFAULT_DEVICE_SN"));
        for (Map.Entry<String, List<KsJkryRcxx>> entry : map.entrySet()) {
            String sbxlh = entry.getKey();
            List<KsJkryRcxx> ksJkryRcxxList = entry.getValue();
            List<JkryQdItem> jklsrcxx = new ArrayList<>();
            for (KsJkryRcxx ksJkryRcxx : ksJkryRcxxList) {
                JkryQdItem jkryQdItem = new JkryQdItem();
                jkryQdItem.setZjhm(ksJkryRcxx.getZjh());
                jkryQdItem.setXm(ksJkryRcxx.getXm());
                Date rcsj = new Date();
                if (ksJkryRcxx.getRcsj() != null) {
                    rcsj = ksJkryRcxx.getRcsj();
                }
                jkryQdItem.setSbsj(DateUtil.format(rcsj, DateUtil.DEFAULT_DATE_TIME));
                jkryQdItem.setKddm(ksJkryRcxx.getKdbh());
                jkryQdItem.setKch(ksJkryRcxx.getKcbh());

                String yzfs = ksJkryRcxx.getYzfs();
                String yzjg = ksJkryRcxx.getYzjg();
                if (StringUtils.contains(yzfs, SfhySftgEnum.SFZYZ.getCode())) {
                    int index = yzfs.indexOf(SfhySftgEnum.SFZYZ.getCode());
                    String jg = String.valueOf(yzjg.charAt(index));
                    if (StringUtils.equals(jg, SfhySftgEnum.TG.getCode())) {
                        jkryQdItem.setSzjg("1");
                    } else {
                        jkryQdItem.setSzjg("0");
                    }
                }
                if (StringUtils.contains(yzfs, SfhySftgEnum.RLYZ.getCode())) {
                    int index = yzfs.indexOf(SfhySftgEnum.RLYZ.getCode());
                    String jg = String.valueOf(yzjg.charAt(index));
                    if (StringUtils.equals(jg, SfhySftgEnum.TG.getCode())) {
                        jkryQdItem.setRlsbjg("1");
                    } else {
                        jkryQdItem.setRlsbjg("0");
                    }
                }
                if (StringUtils.contains(yzfs, SfhySftgEnum.ZWYZ.getCode())) {
                    int index = yzfs.indexOf(SfhySftgEnum.ZWYZ.getCode());
                    String jg = String.valueOf(yzjg.charAt(index));
                    if (StringUtils.equals(jg, SfhySftgEnum.TG.getCode())) {
                        jkryQdItem.setZwrzjg("1");
                    } else {
                        jkryQdItem.setZwrzjg("0");
                    }
                }

                jkryQdItem.setRgyzjg(ksJkryRcxx.getRgyzjg());
                jkryQdItem.setJklsxh(ksJkryRcxx.getJklsxh());
                jkryQdItem.setXsd(ksJkryRcxx.getXsd());
                jklsrcxx.add(jkryQdItem);
            }

            ValidateExamDO validateExamDO = new ValidateExamDO();
            validateExamDO.setExamPlanCode(ksjhbh);
            validateExamDO.setExamSeqCode(ccm);
            JkryQdDto jkryQdDto = new JkryQdDto();
            jkryQdDto.setExam(validateExamDO);
            jkryQdDto.setJklsrcxx(jklsrcxx);

            String encrptJson = provincePlatformClient.sm4EncryptWithDefaultKey(JSONObject.toJSONString(jkryQdDto));

            JkryQdReq jkryQdReq = new JkryQdReq();
            jkryQdReq.setEncrptJson(encrptJson);
            jkryQdReq.setSbxlh(sbxlh);

            Wrapper response = provincePlatformClient.jkryQd(jkryQdReq);

            List<String> ids = ksJkryRcxxList.stream().map(KsJkryRcxx::getJkryrcid).collect(Collectors.toList());
            Example ksjkryrcxxExample = new Example(KsJkryRcxx.class);
            ksjkryrcxxExample.createCriteria().andIn("jkryrcid", ids);
            if (response != null && response.getCode() == 200) {
                log.info("考试计划计划：{}，场次：{} ，监考签到情况上报成功！", ksjhbh, ccm);
                KsJkryRcxx entity = new KsJkryRcxx();
                entity.setSbzt(JkryrcxxSbztEnum.ysb.getCode());
                entity.setSbsj(new Date());
                entity.setUpdateTime(new Date());
                ksJkryRcxxService.updateByExampleSelective(entity, ksjkryrcxxExample);
            } else {
                log.info("考试计划计划：{}，场次：{} ，监考签到情况上报失败 ：{}", ksjhbh, ccm, response.getMessage());
                KsJkryRcxx entity = new KsJkryRcxx();
                entity.setSbzt(JkryrcxxSbztEnum.sbsb.getCode());
                entity.setSbsj(new Date());
                entity.setUpdateTime(new Date());
                ksJkryRcxxService.updateByExampleSelective(entity, ksjkryrcxxExample);
            }
        }

    }

    @Override
    public void uploadJkzpxx(String ksjhbh, String ccm) {
        Example example = new Example(KsJkryRcxx.class);
        example.createCriteria()
                .andEqualTo("ksjhdm", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("sfrc", SfhySftgEnum.YRC.getCode())
                .andIsNotNull("rcrlzp");
        example.and()
                .andNotEqualTo("tbzt", JkryrcxxSbztEnum.ysb.getCode())
                .orIsNull("tbzt");

        List<KsJkryRcxx> ksJkryRcxxes = ksJkryRcxxService.selectListByExample(example);
        if (CollectionUtils.isEmpty(ksJkryRcxxes)) {
            log.info("考试计划计划：{}，场次：{} ，没有需要上报的监考照片信息！", ksjhbh, ccm);
            return;
        }

        Map<String, List<KsJkryRcxx>> map = ksJkryRcxxes.stream().collect(Collectors.groupingBy(item -> StringUtil.isNotBlank(item.getSbxlh()) ? item.getSbxlh() : "DEFAULT_DEVICE_SN"));
        for (Map.Entry<String, List<KsJkryRcxx>> entry : map.entrySet()) {
            String sbxlh = entry.getKey();
            List<KsJkryRcxx> ksJkryRcxxList = entry.getValue();

            ArrayList<String> ids = new ArrayList<>();
            ArrayList<JKqdzpItem> jKqdzpItems = new ArrayList<>();
            for (KsJkryRcxx ksJkryRcxx : ksJkryRcxxList) {
                String photoUrl = upperHsDfsHandler.uploadPhotoToUpperPlatform(ksJkryRcxx.getRcrlzp(), "rcrlzp_" + ksJkryRcxx.getZjh() + ".jpg");
                if (StringUtils.isBlank(photoUrl)) {
                    log.warn("考试计划编号：{}，场次：{}，证件号：{}，照片上传dfs失败！", ksjhbh, ccm, ksJkryRcxx.getZjh());
                    continue;
                }

                JKqdzpItem item = new JKqdzpItem();
                item.setZjhm(ksJkryRcxx.getZjh());
                item.setKddm(ksJkryRcxx.getKdbh());
                item.setKch(ksJkryRcxx.getKcbh());
                item.setQdzp(photoUrl);
                jKqdzpItems.add(item);
                ids.add(ksJkryRcxx.getJkryrcid());
            }
            if (CollectionUtils.isEmpty(jKqdzpItems)) {
                log.info("考试计划计划：{}，场次：{} ，设备序列号：{}，可上报的监考照片信息为空！", ksjhbh, ccm, sbxlh);
                continue;
            }

            ValidateExamDO validateExamDO = new ValidateExamDO();
            validateExamDO.setExamPlanCode(ksjhbh);
            validateExamDO.setExamSeqCode(ccm);

            JkqdzpDto jkqdzpDto = new JkqdzpDto();
            jkqdzpDto.setExam(validateExamDO);
            jkqdzpDto.setJklsrcxx(jKqdzpItems);

            String encrptJson = provincePlatformClient.sm4EncryptWithDefaultKey(JSONObject.toJSONString(jkqdzpDto));
            JkqdZpReq jkqdZpReq = new JkqdZpReq();
            jkqdZpReq.setEncrptJson(encrptJson);
            jkqdZpReq.setSbxlh(sbxlh);

            Wrapper response = provincePlatformClient.jkqdzp(jkqdZpReq);
            Example ksjkryrcxxExample = new Example(KsJkryRcxx.class);
            ksjkryrcxxExample.createCriteria().andIn("jkryrcid", ids);
            if (response != null && response.getCode() == 200) {
                log.info("考试计划计划：{}，场次：{} ，监考签到照片上报成功！", ksjhbh, ccm);
                KsJkryRcxx entity = new KsJkryRcxx();
                entity.setTbzt(JkryrcxxSbztEnum.ysb.getCode());
                entity.setSbsj(new Date());
                entity.setUpdateTime(new Date());
                ksJkryRcxxService.updateByExampleSelective(entity, ksjkryrcxxExample);
            } else {
                log.info("考试计划计划：{}，场次：{} ，监考签到照片上报失败 ：{}", ksjhbh, ccm, response.getMessage());
                KsJkryRcxx entity = new KsJkryRcxx();
                entity.setTbzt(JkryrcxxSbztEnum.sbsb.getCode());
                entity.setSbsj(new Date());
                entity.setUpdateTime(new Date());
                ksJkryRcxxService.updateByExampleSelective(entity, ksjkryrcxxExample);
            }
        }
    }

    @Override
    public void uploadKkwxx(String ksjhbh, String ccm) {
        try {
            // 查询未上报的空考位数据
            List<KsKkwMsg> unReportedKkwMsgs = ksKkwMsgService.findUnReportedKkwMsgs(ksjhbh, ccm);

            if (CollectionUtils.isEmpty(unReportedKkwMsgs)) {
                log.info("计划：{}，场次：{} ，无需要上报的空考位信息！", ksjhbh, ccm);
                return;
            }

            log.info("找到{}条未上报的空考位数据", unReportedKkwMsgs.size());

            // 转换数据格式并按设备序列号分组
            Map<String, SbkkwDTO> deviceSbkkwDTOMap = ksKkwMsgService.convertToSbkkwDTOMap(unReportedKkwMsgs);

            // 按设备分组获取原始数据，用于状态更新
            Map<String, List<KsKkwMsg>> groupedByDevice = unReportedKkwMsgs.stream()
                    .collect(Collectors.groupingBy(KsKkwMsg::getSn));

            for (Map.Entry<String, SbkkwDTO> entry : deviceSbkkwDTOMap.entrySet()) {
                String deviceSn = entry.getKey();
                SbkkwDTO sbkkwDTO = entry.getValue();
                List<KsKkwMsg> deviceKkwMsgs = groupedByDevice.get(deviceSn);

                try {
                    // 构建请求
                    SbkkwReq sbkkwReq = provincePlatformClient.buildSbkkwRequest(sbkkwDTO, deviceSn);

                    // 调用上级平台接口
                    Wrapper<Void> response = provincePlatformClient.sbkkw(sbkkwReq);

                    if (response != null && response.success()) {
                        // 更新上报状态为成功
                        ksKkwMsgService.batchUpdateReportStatus(deviceKkwMsgs, "1");
                        log.info("设备[{}]空考位数据上报成功，共{}条", deviceSn, deviceKkwMsgs.size());
                    } else {
                        // 更新上报状态为失败
                        String errorMsg = response != null ? response.getMessage() : "未知错误";
                        ksKkwMsgService.batchUpdateReportStatus(deviceKkwMsgs, "-1");
                        log.error("设备[{}]空考位数据上报失败：{}", deviceSn, errorMsg);
                    }
                } catch (Exception e) {
                    // 更新上报状态为失败
                    ksKkwMsgService.batchUpdateReportStatus(deviceKkwMsgs, "-1");
                    log.error("设备[{}]空考位数据上报异常", deviceSn, e);
                }
            }

            log.info("空考位数据上报完成，考试计划编号：{}，场次码：{}", ksjhbh, ccm);

        } catch (Exception e) {
            log.error("空考位数据上报异常，考试计划编号：{}，场次码：{}", ksjhbh, ccm, e);
            throw new IdentityVerifyException("空考位数据上报失败：" + e.getMessage());
        }
    }
}
