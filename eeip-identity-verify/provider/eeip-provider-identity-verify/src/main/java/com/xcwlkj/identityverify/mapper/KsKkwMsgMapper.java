/**
 * xcwlkj.com Inc.
 * Copyright (c) 2025-2035 All Rights Reserved.
 */
package com.xcwlkj.identityverify.mapper;

import com.xcwlkj.identityverify.config.commonMapper.CustomMapper;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwTjDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.KkwXqDTO;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjInfoVO;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjItem;
import com.xcwlkj.identityverify.model.vo.cxtj.KkwTjXqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.xcwlkj.identityverify.model.domain.KsKkwMsg;

import java.util.List;

/**
 * 空考位消息数据库操作
 * <AUTHOR>
 * @version $Id: KsKkwMsgMapper.java, v 0.1 2025年07月28日 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface KsKkwMsgMapper extends CustomMapper<KsKkwMsg> {

    /**
     * 查询空考位统计列表
     * @param dto 查询条件
     * @return 空考位统计列表
     */
    List<KkwTjItem> selectKkwTjLb(KkwTjDTO dto);

    /**
     * 查询空考位统计摘要信息
     * @param dto 查询条件
     * @return 统计摘要信息
     */
    KkwTjInfoVO selectKkwTjInfo(KkwTjDTO dto);

    /**
     * 查询空考位详情列表
     * @param dto 查询条件
     * @return 空考位详情列表
     */
    List<KkwTjXqVO> selectKkwTjXq(KkwXqDTO dto);

    /**
     * 查询未上报的空考位消息
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @return 未上报的空考位消息列表
     */
    List<KsKkwMsg> selectUnReportedKkwMsgs(@Param("ksjhbh") String ksjhbh, @Param("ccm") String ccm);
}
