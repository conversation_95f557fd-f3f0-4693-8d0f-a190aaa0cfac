package com.xcwlkj.identityverify.service;

import com.xcwlkj.identityverify.model.domain.KsKkwMsg;
import com.xcwlkj.identityverify.model.domain.KsKsjh;
import com.xcwlkj.identityverify.model.domain.JySysDict;
import com.xcwlkj.identityverify.model.enums.KsjhCjlxEnum;
import com.xcwlkj.identityverify.model.enums.SfhySftgEnum;
import com.xcwlkj.identityverify.model.constant.GlobalKeys;
import com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient;
import com.xcwlkj.identityverify.provincePlatform.request.SbkkwReq;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SbkkwDTO;
import com.xcwlkj.identityverify.service.JySysDictService;
import com.xcwlkj.identityverify.service.KsKkwMsgService;
import com.xcwlkj.identityverify.service.KsKsjhService;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.identityverify.third.superior.http.service.PlatformDataService;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.model.wrapper.Wrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 空考位上报功能测试
 */
@ExtendWith(MockitoExtension.class)
public class PlatformDataServiceKkwUploadTest {

    @Mock
    private KsKsjhService ksKsjhService;
    
    @Mock
    private JySysDictService jySysDictService;
    
    @Mock
    private KsKkwMsgService ksKkwMsgService;
    
    @Mock
    private ProvincePlatformClient provincePlatformClient;
    
    @InjectMocks
    private com.xcwlkj.identityverify.third.superior.http.service.impl.PlatformDataServiceImpl platformDataService;

    private String testKsjhbh = "TEST_KSJHBH_001";
    private String testCcm = "001";

    @BeforeEach
    void setUp() {
        // 设置考试计划
        KsKsjh ksKsjh = new KsKsjh();
        ksKsjh.setKsjhbh(testKsjhbh);
        ksKsjh.setCjlx(KsjhCjlxEnum.SJPTDR.getCode());
        when(ksKsjhService.selectSingleByKey(testKsjhbh)).thenReturn(ksKsjh);

        // 设置平台配置
        JySysDict jySysDict = new JySysDict();
        jySysDict.setTValue(SuperiorPlatEnum.HISOME.getCode());
        when(jySysDictService.queryConfigValueByKey(GlobalKeys.DEFAULT_PLAT)).thenReturn(jySysDict);
    }

    @Test
    void testKkwUpload_Success() {
        // 准备测试数据
        List<KsKkwMsg> mockKkwMsgs = createMockKkwMsgs();
        when(ksKkwMsgService.selectListByExample(any(Example.class))).thenReturn(mockKkwMsgs);

        // 模拟省平台客户端
        SbkkwReq mockRequest = new SbkkwReq();
        when(provincePlatformClient.buildSbkkwRequest(any(SbkkwDTO.class), anyString())).thenReturn(mockRequest);
        
        Wrapper<Void> successResponse = new Wrapper<>();
        successResponse.setCode(200);
        successResponse.setMessage("成功");
        when(provincePlatformClient.sbkkw(any(SbkkwReq.class))).thenReturn(successResponse);

        // 执行测试
        platformDataService.kkwUpload(testKsjhbh, testCcm);

        // 验证调用
        verify(ksKkwMsgService).selectListByExample(any(Example.class));
        verify(provincePlatformClient).buildSbkkwRequest(any(SbkkwDTO.class), anyString());
        verify(provincePlatformClient).sbkkw(any(SbkkwReq.class));
        verify(ksKkwMsgService, atLeastOnce()).updateByPrimaryKeySelective(any(KsKkwMsg.class));
    }

    @Test
    void testKkwUpload_NoData() {
        // 模拟无数据情况
        when(ksKkwMsgService.selectListByExample(any(Example.class))).thenReturn(new ArrayList<>());

        // 执行测试
        platformDataService.kkwUpload(testKsjhbh, testCcm);

        // 验证不会调用上报接口
        verify(provincePlatformClient, never()).sbkkw(any(SbkkwReq.class));
    }

    @Test
    void testKkwUpload_UnsupportedPlatform() {
        // 设置不支持的平台
        JySysDict jySysDict = new JySysDict();
        jySysDict.setTValue(SuperiorPlatEnum.JS_JF.getCode());
        when(jySysDictService.queryConfigValueByKey(GlobalKeys.DEFAULT_PLAT)).thenReturn(jySysDict);

        // 执行测试
        platformDataService.kkwUpload(testKsjhbh, testCcm);

        // 验证不会调用上报接口
        verify(provincePlatformClient, never()).sbkkw(any(SbkkwReq.class));
    }

    private List<KsKkwMsg> createMockKkwMsgs() {
        List<KsKkwMsg> kkwMsgs = new ArrayList<>();
        
        KsKkwMsg msg1 = new KsKkwMsg();
        msg1.setId("1");
        msg1.setKsjhbh(testKsjhbh);
        msg1.setCcm(testCcm);
        msg1.setBzhkdid("TEST_KDDM");
        msg1.setBzhkcid("TEST_KCID_001");
        msg1.setSn("TEST_SN_001");
        msg1.setDevType("172");
        msg1.setLjkch("001");
        msg1.setKch("001");
        msg1.setKsZkzh("123456789012");
        msg1.setKsBpzwh("01");
        msg1.setKsSjzwh("01");
        msg1.setRcbz("6");
        msg1.setSjyxj(20);
        msg1.setSczt(ScztEnum.NOTDEL.getCode());
        msg1.setReportFlag("0");
        msg1.setCzsj(new Date());
        msg1.setSfrc(SfhySftgEnum.TG.getCode());
        msg1.setYzfs("12");
        msg1.setYzjg("11");
        msg1.setRcsj("2025-01-01 10:00:00");
        
        KsKkwMsg msg2 = new KsKkwMsg();
        msg2.setId("2");
        msg2.setKsjhbh(testKsjhbh);
        msg2.setCcm(testCcm);
        msg2.setBzhkdid("TEST_KDDM");
        msg2.setBzhkcid("TEST_KCID_001");
        msg2.setSn("TEST_SN_001");
        msg2.setDevType("172");
        msg2.setLjkch("001");
        msg2.setKch("001");
        msg2.setKsZkzh("123456789013");
        msg2.setKsBpzwh("02");
        msg2.setKsSjzwh("02");
        msg2.setRcbz("6");
        msg2.setSjyxj(20);
        msg2.setSczt(ScztEnum.NOTDEL.getCode());
        msg2.setReportFlag("0");
        msg2.setCzsj(new Date());
        
        kkwMsgs.add(msg1);
        kkwMsgs.add(msg2);
        
        return kkwMsgs;
    }
}
